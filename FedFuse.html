<!DOCTYPE html>
<!-- saved from url=(0151)https://openreview.net/forum?id=9ycNV8yhw8&referrer=%5BAuthor%20Console%5D(%2Fgroup%3Fid%3DNeurIPS.cc%2F2025%2FConference%2FAuthors%23your-submissions) -->
<html lang="en"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1"><link rel="preload" as="image" href="./FedFuse_files/arrow_left.svg"><link rel="preload" as="image" href="./FedFuse_files/pdf_icon_blue.svg"><link rel="stylesheet" href="./FedFuse_files/623ec4d945fb0950.css" data-precedence="next"><link rel="stylesheet" href="./FedFuse_files/7efbc204b5b07ee6.css" data-precedence="next"><link rel="stylesheet" href="./FedFuse_files/8e7bf7099e0c2325.css" data-precedence="next"><link rel="stylesheet" href="./FedFuse_files/7e1ff74241679440.css" data-precedence="next"><link rel="stylesheet" href="./FedFuse_files/788c496c60915883.css" data-precedence="next"><link rel="stylesheet" href="./FedFuse_files/9dffa5889d3de130.css" data-precedence="next"><link rel="preload" as="script" fetchpriority="low" href="./FedFuse_files/webpack-9660e8bc1b33b385.js.download"><script src="./FedFuse_files/4bd1b696-674b1201a1d63716.js.download" async=""></script><script src="./FedFuse_files/1684-f2e76e08982cc2da.js.download" async=""></script><script src="./FedFuse_files/main-app-895d0017b454980b.js.download" async=""></script><script src="./FedFuse_files/e37a0b60-86dcf540460bd9a6.js.download" async=""></script><script src="./FedFuse_files/7ce798d6-3eb8122476a3f2e5.js.download" async=""></script><script src="./FedFuse_files/6874-8f3d6c72a87c2225.js.download" async=""></script><script src="./FedFuse_files/3697-c0092b2c69fd8d8c.js.download" async=""></script><script src="./FedFuse_files/590-a7095dbf68fad767.js.download" async=""></script><script src="./FedFuse_files/4540-1380528b77b77034.js.download" async=""></script><script src="./FedFuse_files/6325-93a1b42c84bba41c.js.download" async=""></script><script src="./FedFuse_files/8780-219cd1efe9e9c581.js.download" async=""></script><script src="./FedFuse_files/5226-3f9b6d1f505acbb7.js.download" async=""></script><script src="./FedFuse_files/5930-ff1d66cb0fff6ec4.js.download" async=""></script><script src="./FedFuse_files/9433-4d56475ee6e9848e.js.download" async=""></script><script src="./FedFuse_files/layout-96cbf563e166e8f5.js.download" async=""></script><script src="./FedFuse_files/6846-1cd184c18c60fea8.js.download" async=""></script><script src="./FedFuse_files/1592-72921a7113ac822e.js.download" async=""></script><script src="./FedFuse_files/9032-8fb8561574ebe0ce.js.download" async=""></script><script src="./FedFuse_files/9251-4963718882dacbdc.js.download" async=""></script><script src="./FedFuse_files/2882-66622e31b0f52a02.js.download" async=""></script><script src="./FedFuse_files/1399-071a12ebad7508dd.js.download" async=""></script><script src="./FedFuse_files/4757-73521c726a0e013c.js.download" async=""></script><script src="./FedFuse_files/4745-960031cc83ae4c6c.js.download" async=""></script><script src="./FedFuse_files/3474-fc09b016da95fe81.js.download" async=""></script><script src="./FedFuse_files/5262-e24c2f70e61a06c6.js.download" async=""></script><script src="./FedFuse_files/page-ae19d00834327398.js.download" async=""></script><script src="./FedFuse_files/error-b150b0fce1b36d7b.js.download" async=""></script><script src="./FedFuse_files/global-error-437204fce1f23329.js.download" async=""></script><link rel="preload" href="./FedFuse_files/tex-chtml-full.js.download" as="script"><link rel="preload" href="./FedFuse_files/api.js.download" as="script"><link rel="preload" href="./FedFuse_files/js" as="script"><meta name="next-size-adjust" content=""><link rel="icon" href="https://openreview.net/favicon.ico"><link rel="manifest" href="https://openreview.net/manifest.json"><title>FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning | OpenReview</title><meta name="description" content="Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during aggregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency."><meta name="citation_title" content="FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning"><meta name="citation_author" content="Haizhou Du"><meta name="citation_author" content="Yiran Xiang"><meta name="citation_author" content="Lixin Huang"><meta name="citation_author" content="Xiufeng Liu"><meta name="citation_author" content="Huan Huo"><meta name="citation_author" content="Zonghan Wu"><meta name="citation_author" content="Guodong Long"><meta name="citation_publication_date" content="2025/05/10"><meta name="citation_pdf_url" content="https://openreview.net/pdf?id=9ycNV8yhw8"><meta name="citation_abstract" content="Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during aggregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency."><meta property="og:title" content="FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion..."><meta property="og:description" content="Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to..."><meta property="og:type" content="article"><meta name="twitter:card" content="summary"><meta name="twitter:title" content="FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion..."><meta name="twitter:description" content="Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to..."><script src="./FedFuse_files/polyfills-42372ed130431b0a.js.download" nomodule=""></script><script src="./FedFuse_files/api.js.download"></script><link rel="preload" href="https://openreview.net/_next/static/media/08f4947ad4536ee1-s.p.woff2" as="font" crossorigin="" type="font/woff2"><link rel="preload" href="https://openreview.net/_next/static/media/c4250770ab8708b6-s.p.woff2" as="font" crossorigin="" type="font/woff2"><script src="./FedFuse_files/safe.js.download" charset="UTF-8"></script><style type="text/css">.CtxtMenu_InfoClose {  top:.2em; right:.2em;}
.CtxtMenu_InfoContent {  overflow:auto; text-align:left; font-size:80%;  padding:.4em .6em; border:1px inset; margin:1em 0px;  max-height:20em; max-width:30em; background-color:#EEEEEE;  white-space:normal;}
.CtxtMenu_Info.CtxtMenu_MousePost {outline:none;}
.CtxtMenu_Info {  position:fixed; left:50%; width:auto; text-align:center;  border:3px outset; padding:1em 2em; background-color:#DDDDDD;  color:black;  cursor:default; font-family:message-box; font-size:120%;  font-style:normal; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 15px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius:15px;               /* Safari and Chrome */  -moz-border-radius:15px;                  /* Firefox */  -khtml-border-radius:15px;                /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */  filter:progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color="gray", Positive="true"); /* IE */}
</style><style type="text/css">.CtxtMenu_MenuClose {  position:absolute;  cursor:pointer;  display:inline-block;  border:2px solid #AAA;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  font-family: "Courier New", Courier;  font-size:24px;  color:#F0F0F0}
.CtxtMenu_MenuClose span {  display:block; background-color:#AAA; border:1.5px solid;  border-radius:18px;  -webkit-border-radius: 18px;             /* Safari and Chrome */  -moz-border-radius: 18px;                /* Firefox */  -khtml-border-radius: 18px;              /* Konqueror */  line-height:0;  padding:8px 0 6px     /* may need to be browser-specific */}
.CtxtMenu_MenuClose:hover {  color:white!important;  border:2px solid #CCC!important}
.CtxtMenu_MenuClose:hover span {  background-color:#CCC!important}
.CtxtMenu_MenuClose:hover:focus {  outline:none}
</style><style type="text/css">.CtxtMenu_Menu {  position:absolute;  background-color:white;  color:black;  width:auto; padding:5px 0px;  border:1px solid #CCCCCC; margin:0; cursor:default;  font: menu; text-align:left; text-indent:0; text-transform:none;  line-height:normal; letter-spacing:normal; word-spacing:normal;  word-wrap:normal; white-space:nowrap; float:none; z-index:201;  border-radius: 5px;                     /* Opera 10.5 and IE9 */  -webkit-border-radius: 5px;             /* Safari and Chrome */  -moz-border-radius: 5px;                /* Firefox */  -khtml-border-radius: 5px;              /* Konqueror */  box-shadow:0px 10px 20px #808080;         /* Opera 10.5 and IE9 */  -webkit-box-shadow:0px 10px 20px #808080; /* Safari 3 & Chrome */  -moz-box-shadow:0px 10px 20px #808080;    /* Forefox 3.5 */  -khtml-box-shadow:0px 10px 20px #808080;  /* Konqueror */}
.CtxtMenu_MenuItem {  padding: 1px 2em;  background:transparent;}
.CtxtMenu_MenuArrow {  position:absolute; right:.5em; padding-top:.25em; color:#666666;  font-family: null; font-size: .75em}
.CtxtMenu_MenuActive .CtxtMenu_MenuArrow {color:white}
.CtxtMenu_MenuArrow.CtxtMenu_RTL {left:.5em; right:auto}
.CtxtMenu_MenuCheck {  position:absolute; left:.7em;  font-family: null}
.CtxtMenu_MenuCheck.CtxtMenu_RTL { right:.7em; left:auto }
.CtxtMenu_MenuRadioCheck {  position:absolute; left: .7em;}
.CtxtMenu_MenuRadioCheck.CtxtMenu_RTL {  right: .7em; left:auto}
.CtxtMenu_MenuInputBox {  padding-left: 1em; right:.5em; color:#666666;  font-family: null;}
.CtxtMenu_MenuInputBox.CtxtMenu_RTL {  left: .1em;}
.CtxtMenu_MenuComboBox {  left:.1em; padding-bottom:.5em;}
.CtxtMenu_MenuSlider {  left: .1em;}
.CtxtMenu_SliderValue {  position:absolute; right:.1em; padding-top:.25em; color:#333333;  font-size: .75em}
.CtxtMenu_SliderBar {  outline: none; background: #d3d3d3}
.CtxtMenu_MenuLabel {  padding: 1px 2em 3px 1.33em;  font-style:italic}
.CtxtMenu_MenuRule {  border-top: 1px solid #DDDDDD;  margin: 4px 3px;}
.CtxtMenu_MenuDisabled {  color:GrayText}
.CtxtMenu_MenuActive {  background-color: #606872;  color: white;}
.CtxtMenu_MenuDisabled:focus {  background-color: #E8E8E8}
.CtxtMenu_MenuLabel:focus {  background-color: #E8E8E8}
.CtxtMenu_ContextMenu:focus {  outline:none}
.CtxtMenu_ContextMenu .CtxtMenu_MenuItem:focus {  outline:none}
.CtxtMenu_SelectionMenu {  position:relative; float:left;  border-bottom: none; -webkit-box-shadow:none; -webkit-border-radius:0px; }
.CtxtMenu_SelectionItem {  padding-right: 1em;}
.CtxtMenu_Selection {  right: 40%; width:50%; }
.CtxtMenu_SelectionBox {  padding: 0em; max-height:20em; max-width: none;  background-color:#FFFFFF;}
.CtxtMenu_SelectionDivider {  clear: both; border-top: 2px solid #000000;}
.CtxtMenu_Menu .CtxtMenu_MenuClose {  top:-10px; left:-10px}
</style><style data-emotion="css" data-s=""></style><style id="MJX-CHTML-styles">
mjx-container[jax="CHTML"] {
  line-height: 0;
}

mjx-container [space="1"] {
  margin-left: .111em;
}

mjx-container [space="2"] {
  margin-left: .167em;
}

mjx-container [space="3"] {
  margin-left: .222em;
}

mjx-container [space="4"] {
  margin-left: .278em;
}

mjx-container [space="5"] {
  margin-left: .333em;
}

mjx-container [rspace="1"] {
  margin-right: .111em;
}

mjx-container [rspace="2"] {
  margin-right: .167em;
}

mjx-container [rspace="3"] {
  margin-right: .222em;
}

mjx-container [rspace="4"] {
  margin-right: .278em;
}

mjx-container [rspace="5"] {
  margin-right: .333em;
}

mjx-container [size="s"] {
  font-size: 70.7%;
}

mjx-container [size="ss"] {
  font-size: 50%;
}

mjx-container [size="Tn"] {
  font-size: 60%;
}

mjx-container [size="sm"] {
  font-size: 85%;
}

mjx-container [size="lg"] {
  font-size: 120%;
}

mjx-container [size="Lg"] {
  font-size: 144%;
}

mjx-container [size="LG"] {
  font-size: 173%;
}

mjx-container [size="hg"] {
  font-size: 207%;
}

mjx-container [size="HG"] {
  font-size: 249%;
}

mjx-container [width="full"] {
  width: 100%;
}

mjx-box {
  display: inline-block;
}

mjx-block {
  display: block;
}

mjx-itable {
  display: inline-table;
}

mjx-row {
  display: table-row;
}

mjx-row > * {
  display: table-cell;
}

mjx-mtext {
  display: inline-block;
}

mjx-mstyle {
  display: inline-block;
}

mjx-merror {
  display: inline-block;
  color: red;
  background-color: yellow;
}

mjx-mphantom {
  visibility: hidden;
}

_::-webkit-full-page-media, _:future, :root mjx-container {
  will-change: opacity;
}

mjx-assistive-mml {
  position: absolute !important;
  top: 0px;
  left: 0px;
  clip: rect(1px, 1px, 1px, 1px);
  padding: 1px 0px 0px 0px !important;
  border: 0px !important;
  display: block !important;
  width: auto !important;
  overflow: hidden !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

mjx-assistive-mml[display="block"] {
  width: 100% !important;
}

mjx-c::before {
  display: block;
  width: 0;
}

.MJX-TEX {
  font-family: MJXZERO, MJXTEX;
}

.TEX-B {
  font-family: MJXZERO, MJXTEX-B;
}

.TEX-I {
  font-family: MJXZERO, MJXTEX-I;
}

.TEX-MI {
  font-family: MJXZERO, MJXTEX-MI;
}

.TEX-BI {
  font-family: MJXZERO, MJXTEX-BI;
}

.TEX-S1 {
  font-family: MJXZERO, MJXTEX-S1;
}

.TEX-S2 {
  font-family: MJXZERO, MJXTEX-S2;
}

.TEX-S3 {
  font-family: MJXZERO, MJXTEX-S3;
}

.TEX-S4 {
  font-family: MJXZERO, MJXTEX-S4;
}

.TEX-A {
  font-family: MJXZERO, MJXTEX-A;
}

.TEX-C {
  font-family: MJXZERO, MJXTEX-C;
}

.TEX-CB {
  font-family: MJXZERO, MJXTEX-CB;
}

.TEX-FR {
  font-family: MJXZERO, MJXTEX-FR;
}

.TEX-FRB {
  font-family: MJXZERO, MJXTEX-FRB;
}

.TEX-SS {
  font-family: MJXZERO, MJXTEX-SS;
}

.TEX-SSB {
  font-family: MJXZERO, MJXTEX-SSB;
}

.TEX-SSI {
  font-family: MJXZERO, MJXTEX-SSI;
}

.TEX-SC {
  font-family: MJXZERO, MJXTEX-SC;
}

.TEX-T {
  font-family: MJXZERO, MJXTEX-T;
}

.TEX-V {
  font-family: MJXZERO, MJXTEX-V;
}

.TEX-VB {
  font-family: MJXZERO, MJXTEX-VB;
}

mjx-stretchy-v mjx-c, mjx-stretchy-h mjx-c {
  font-family: MJXZERO, MJXTEX-S1, MJXTEX-S4, MJXTEX, MJXTEX-A ! important;
}

@font-face /* 0 */ {
  font-family: MJXZERO;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Zero.woff") format("woff");
}

@font-face /* 1 */ {
  font-family: MJXTEX;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Main-Regular.woff") format("woff");
}

@font-face /* 2 */ {
  font-family: MJXTEX-B;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Main-Bold.woff") format("woff");
}

@font-face /* 3 */ {
  font-family: MJXTEX-I;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Math-Italic.woff") format("woff");
}

@font-face /* 4 */ {
  font-family: MJXTEX-MI;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Main-Italic.woff") format("woff");
}

@font-face /* 5 */ {
  font-family: MJXTEX-BI;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Math-BoldItalic.woff") format("woff");
}

@font-face /* 6 */ {
  font-family: MJXTEX-S1;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Size1-Regular.woff") format("woff");
}

@font-face /* 7 */ {
  font-family: MJXTEX-S2;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Size2-Regular.woff") format("woff");
}

@font-face /* 8 */ {
  font-family: MJXTEX-S3;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Size3-Regular.woff") format("woff");
}

@font-face /* 9 */ {
  font-family: MJXTEX-S4;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Size4-Regular.woff") format("woff");
}

@font-face /* 10 */ {
  font-family: MJXTEX-A;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_AMS-Regular.woff") format("woff");
}

@font-face /* 11 */ {
  font-family: MJXTEX-C;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Calligraphic-Regular.woff") format("woff");
}

@font-face /* 12 */ {
  font-family: MJXTEX-CB;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Calligraphic-Bold.woff") format("woff");
}

@font-face /* 13 */ {
  font-family: MJXTEX-FR;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Fraktur-Regular.woff") format("woff");
}

@font-face /* 14 */ {
  font-family: MJXTEX-FRB;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Fraktur-Bold.woff") format("woff");
}

@font-face /* 15 */ {
  font-family: MJXTEX-SS;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_SansSerif-Regular.woff") format("woff");
}

@font-face /* 16 */ {
  font-family: MJXTEX-SSB;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_SansSerif-Bold.woff") format("woff");
}

@font-face /* 17 */ {
  font-family: MJXTEX-SSI;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_SansSerif-Italic.woff") format("woff");
}

@font-face /* 18 */ {
  font-family: MJXTEX-SC;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Script-Regular.woff") format("woff");
}

@font-face /* 19 */ {
  font-family: MJXTEX-T;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Typewriter-Regular.woff") format("woff");
}

@font-face /* 20 */ {
  font-family: MJXTEX-V;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Vector-Regular.woff") format("woff");
}

@font-face /* 21 */ {
  font-family: MJXTEX-VB;
  src: url("https://cdn.jsdelivr.net/npm/mathjax@3.2.2/es5/output/chtml/fonts/woff-v2/MathJax_Vector-Bold.woff") format("woff");
}
</style></head><body class="__className_086c6e"><div id="__next"><nav class="navbar navbar-inverse" role="navigation"><div class="container"><div class="navbar-header"><button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar"><span class="sr-only">Toggle navigation</span><span class="icon-bar"></span><span class="icon-bar"></span><span class="icon-bar"></span></button><a class="navbar-brand home push-link" href="https://openreview.net/"><strong>OpenReview</strong>.net</a></div><div id="navbar" class="navbar-collapse collapse"><form class="navbar-form navbar-left profile-search" role="search"><div class="form-group has-feedback"><input type="text" class="form-control" placeholder="Search OpenReview..." autocomplete="off" autocorrect="off" name="term" value=""><span class="glyphicon glyphicon-search form-control-feedback" aria-hidden="true"></span></div><input type="hidden" name="group" value="all"><input type="hidden" name="content" value="all"><input type="hidden" name="source" value="all"></form><ul class="nav navbar-nav navbar-right"><li class="hidden-sm"><a href="https://openreview.net/notifications">Notifications<!--$--><span class="badge">225</span><!--/$--></a></li><li class="hidden-sm"><a href="https://openreview.net/activity">Activity</a></li><li class="hidden-sm"><a href="https://openreview.net/tasks">Tasks</a></li><li id="user-menu" class="dropdown"><a class="dropdown-toggle" data-toggle="dropdown" role="button" aria-haspopup="true" aria-expanded="false"><span>Xiufeng Liu</span> <span class="caret"></span></a><ul class="dropdown-menu"><li><a href="https://openreview.net/profile">Profile</a></li><li class="visible-sm-block"><a href="https://openreview.net/notifications">Notifications<!--$--><span class="badge">225</span><!--/$--></a></li><li class="visible-sm-block"><a href="https://openreview.net/activity">Activity</a></li><li class="visible-sm-block"><a href="https://openreview.net/tasks">Tasks</a></li><li role="separator" class="divider hidden-xs"></li><li><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;referrer=%5BAuthor%20Console%5D(%2Fgroup%3Fid%3DNeurIPS.cc%2F2025%2FConference%2FAuthors%23your-submissions)#">Logout</a></li></ul></li></ul></div></div></nav><div id="flash-message-container" class="alert alert-danger fixed-overlay" role="alert" style="display:none"><div class="container"><div class="row"><div class="col-xs-12"><div class="alert-content"><button type="button" class="close" aria-label="Close"><span aria-hidden="true">×</span></button></div></div></div></div></div><script>(self.__next_s=self.__next_s||[]).push(["https://challenges.cloudflare.com/turnstile/v0/api.js",{}])</script><div id="bibtex-modal" class="modal fade" tabindex="-1" role="dialog"><div class="modal-dialog "><div class="modal-content"><div class="modal-header"><button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">×</span></button><h3 class="modal-title">BibTeX Record</h3></div><div class="modal-body"><pre class="bibtex-content"></pre><em class="instructions">Click anywhere on the box above to highlight complete record</em></div><div class="modal-footer"><button type="button" class="btn btn-default" data-dismiss="modal">Done</button></div></div></div></div><div id="or-banner" class="banner"><div class="container"><div class="row"><div class="col-xs-12"><a title="Back" href="https://openreview.net/group?id=NeurIPS.cc/2025/Conference/Authors#your-submissions"><img class="icon" src="./FedFuse_files/arrow_left.svg" alt="back arrow">Back to<!-- --> <strong>Author Console</strong></a></div></div></div></div><div class="container"><div class="row"><main id="content"><div class="Forum_forum__wS8Fw"><div class="forum-container"><div class="forum-note"><div class="forum-title mt-2 mb-2"><h2 class="citation_title">FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning</h2><div class="forum-content-link"><a class="citation_pdf_url" href="https://openreview.net/pdf?id=9ycNV8yhw8" title="Download PDF" target="_blank" rel="noreferrer"><img src="./FedFuse_files/pdf_icon_blue.svg" alt="Download PDF"></a></div></div><div class="forum-authors mb-2"><h3><span><a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Haizhou_Du2" data-original-title="~Haizhou_Du2">Haizhou Du</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Yiran_Xiang1" data-original-title="~Yiran_Xiang1">Yiran Xiang</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Lixin_Huang3" data-original-title="~Lixin_Huang3">Lixin Huang</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Xiufeng_Liu3" data-original-title="~Xiufeng_Liu3">Xiufeng Liu</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Huan_Huo1" data-original-title="~Huan_Huo1">Huan Huo</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Zonghan_Wu1" data-original-title="~Zonghan_Wu1">Zonghan Wu</a>, <a title="" data-toggle="tooltip" data-placement="top" href="https://openreview.net/profile?id=~Guodong_Long1" data-original-title="~Guodong_Long1">Guodong Long</a> <!-- --> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span></span></h3></div><div class="clearfix mb-1"><div class="forum-meta"><span class="date item"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>10 May 2025 (modified: 28 May 2025)</span><span class="item"><span class="glyphicon glyphicon-folder-open " aria-hidden="true"></span>NeurIPS 2025 Conference Submission</span><span class="readers item" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Conference, Senior Area Chairs, Area Chairs, Reviewers, Authors</span><span class="item"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=9ycNV8yhw8">Revisions</a></span><span class="item"><span class="glyphicon glyphicon-copyright-mark " aria-hidden="true"></span><a href="https://creativecommons.org/licenses/by/4.0/" target="_blank" rel="noopener noreferrer" title="" data-toggle="tooltip" data-placement="top" data-original-title="Licensed under Creative Commons Attribution 4.0 International">CC BY 4.0</a></span></div><div class="invitation-buttons"></div></div><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Keywords<!-- -->:</strong> <span class="note-content-value">Knowledge Distillation, Personalized Federated Learning, Expert-Guided Fusion</span></div><div><strong class="note-content-field disable-tex-rendering">Abstract<!-- -->:</strong> <div class="note-content-value markdown-rendered"><p>Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during aggregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Checklist Confirmation<!-- -->:</strong> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span><span class="note-content-value">I confirm that I have included a paper checklist in the paper PDF.</span></div><div><strong class="note-content-field disable-tex-rendering">Supplementary Material<!-- -->:</strong> <span class="note-content-value"><a href="https://openreview.net/attachment?id=9ycNV8yhw8&amp;name=supplementary_material" class="attachment-download-link" title="Download Supplementary Material" target="_blank"><span class="glyphicon glyphicon-download-alt " aria-hidden="true"></span> <!-- -->zip</a></span></div><div><strong class="note-content-field disable-tex-rendering">Reviewer Nomination<!-- -->:</strong> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span><span class="note-content-value"><EMAIL></span></div><div><strong class="note-content-field disable-tex-rendering">Responsible Reviewing<!-- -->:</strong> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span><span class="note-content-value">We acknowledge the responsible reviewing obligations as authors.</span></div><div><strong class="note-content-field disable-tex-rendering">Primary Area<!-- -->:</strong> <span class="note-content-value">General machine learning (supervised, unsupervised, online, active, etc.)</span></div><div><strong class="note-content-field disable-tex-rendering">LLM Usage<!-- -->:</strong> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span><span class="note-content-value">Not used at all (you can then skip the rest)</span></div><div><strong class="note-content-field disable-tex-rendering">Declaration<!-- -->:</strong> <span class="glyphicon glyphicon-eye-open private-contents-icon" data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Privately revealed to NeurIPS 2025 Conference, NeurIPS 2025 Conference Submission15547 Authors"></span><span class="note-content-value">I confirm that the above information is accurate.</span></div><div><strong class="note-content-field disable-tex-rendering">Submission Number<!-- -->:</strong> <span class="note-content-value">15547</span></div></div></div><div class="filters-container mt-4"><form class="form-inline filter-controls"><div class="wrap"><div class="form-group expand"><div class="replies-filter invitations-filter css-b62m3t-container"><span id="react-select-invitations-filter-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" role="log" class="css-7pg0cj-a11yText"></span><div class="dropdown-select__control css-1tqhi6y-control"><div class="dropdown-select__value-container dropdown-select__value-container--is-multi css-1uzcsaf"><div class="dropdown-select__placeholder css-1m6ztbo-placeholder" id="react-select-invitations-filter-placeholder">Filter by reply type...</div><div class="dropdown-select__input-container css-1ab7ooq" data-value=""><input class="dropdown-select__input" autocapitalize="none" autocomplete="off" autocorrect="off" id="react-select-invitations-filter-input" spellcheck="false" tabindex="0" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" role="combobox" aria-activedescendant="" aria-describedby="react-select-invitations-filter-placeholder" type="text" value="" style="color: inherit; background: 0px center; opacity: 1; width: 100%; grid-area: 1 / 2; font: inherit; min-width: 2px; border: 0px; margin: 0px; outline: 0px; padding: 0px;"></div></div><div class="dropdown-select__indicators css-1wy0on6"><span class="dropdown-select__indicator-separator css-qgckm3-indicatorSeparator"></span><div class="dropdown-select__indicator dropdown-select__dropdown-indicator css-1qajzci-indicatorContainer" aria-hidden="true"><svg height="20" width="20" viewBox="0 0 20 20" aria-hidden="true" focusable="false" class="css-8mmkcg"><path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path></svg></div></div></div><div><input type="hidden" value="" name="filter-invitations"></div></div></div><div class="form-group expand"><div class="replies-filter css-b62m3t-container"><span id="react-select-signatures-filter-live-region" class="css-7pg0cj-a11yText"></span><span aria-live="polite" aria-atomic="false" aria-relevant="additions text" role="log" class="css-7pg0cj-a11yText"></span><div class="dropdown-select__control css-1tqhi6y-control"><div class="dropdown-select__value-container dropdown-select__value-container--is-multi css-1uzcsaf"><div class="dropdown-select__placeholder css-1m6ztbo-placeholder" id="react-select-signatures-filter-placeholder">Filter by author...</div><div class="dropdown-select__input-container css-1ab7ooq" data-value=""><input class="dropdown-select__input" autocapitalize="none" autocomplete="off" autocorrect="off" id="react-select-signatures-filter-input" spellcheck="false" tabindex="0" aria-autocomplete="list" aria-expanded="false" aria-haspopup="true" role="combobox" aria-activedescendant="" aria-describedby="react-select-signatures-filter-placeholder" type="text" value="" style="color: inherit; background: 0px center; opacity: 1; width: 100%; grid-area: 1 / 2; font: inherit; min-width: 2px; border: 0px; margin: 0px; outline: 0px; padding: 0px;"></div></div><div class="dropdown-select__indicators css-1wy0on6"><span class="dropdown-select__indicator-separator css-qgckm3-indicatorSeparator"></span><div class="dropdown-select__indicator dropdown-select__dropdown-indicator css-1qajzci-indicatorContainer" aria-hidden="true"><svg height="20" width="20" viewBox="0 0 20 20" aria-hidden="true" focusable="false" class="css-8mmkcg"><path d="M4.516 7.548c0.436-0.446 1.043-0.481 1.576 0l3.908 3.747 3.908-3.747c0.533-0.481 1.141-0.446 1.574 0 0.436 0.445 0.408 1.197 0 1.615-0.406 0.418-4.695 4.502-4.695 4.502-0.217 0.223-0.502 0.335-0.787 0.335s-0.57-0.112-0.789-0.335c0 0-4.287-4.084-4.695-4.502s-0.436-1.17 0-1.615z"></path></svg></div></div></div><div><input type="hidden" value="" name="filter-signatures"></div></div></div><div class="form-group expand"><input class="form-control" id="keyword-input" placeholder="Search keywords..." maxlength="100" type="text" value=""></div><div class="form-group no-expand"><select id="sort-dropdown" class="form-control"><option value="date-desc">Sort: Newest First</option><option value="date-asc">Sort: Oldest First</option></select></div><div class="form-group no-expand layout-buttons"><div class="btn-group btn-group-sm" role="group" aria-label="nesting level"><button type="button" class="btn btn-default "><img class="icon" alt="back arrow" data-toggle="tooltip" title="Linear discussion layout" src="./FedFuse_files/linear_icon.svg"><span class="sr-only">Linear</span></button><button type="button" class="btn btn-default active"><img class="icon" alt="back arrow" data-toggle="tooltip" title="Threaded discussion layout" src="./FedFuse_files/threaded_icon.svg"><span class="sr-only">Threaded</span></button><button type="button" class="btn btn-default "><img class="icon" alt="back arrow" data-toggle="tooltip" title="Nested discussion layout" src="./FedFuse_files/nested_icon.svg"><span class="sr-only">Nested</span></button></div><div class="btn-group btn-group-sm" role="group" aria-label="collapse level"><button type="button" class="btn btn-default "><span data-toggle="tooltip" title="Collapse content">−</span><span class="sr-only">Collapsed</span></button><button type="button" class="btn btn-default "><span data-toggle="tooltip" title="Partially expand content">＝</span><span class="sr-only">Default</span></button><button type="button" class="btn btn-default active"><span data-toggle="tooltip" title="Fully expand content">≡</span><span class="sr-only">Expanded</span></button></div><div class="btn-group btn-group-sm" role="group" aria-label="copy url"><button type="button" class="btn btn-default"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="Copy filter URL" aria-hidden="true"></span><span class="sr-only">Copy link</span></button></div></div></div><div><label class="control-label icon-label"><span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="Visible to" aria-hidden="true"></span></label><div class="form-group readers-filter-container"><div class="btn-group btn-group-sm toggle-group readers-filter " role="group"><label class="btn btn-default  state-0" data-toggle="tooltip" title="Everyone"><input type="checkbox" value="everyone" name="readers-filter"> Everyone</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Program Chairs"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Program_Chairs" name="readers-filter"> Program Chairs</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Senior Area Chairs"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Area Chairs"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Reviewers Submitted"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Authors"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Authors" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Reviewer ceRT"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Reviewer_ceRT" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Reviewer kjvn"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Reviewer_kjvn" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Reviewer r41q"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Reviewer_r41q" name="readers-filter"> Submission15547...</label><label class="btn btn-default  state-0" data-toggle="tooltip" title="Submission15547 Reviewer Z5Bu"><input type="checkbox" value="NeurIPS.cc/2025/Conference/Submission15547/Reviewer_Z5Bu" name="readers-filter"> Submission15547...</label><label class="btn btn-default reset-btn"><input type="checkbox" value="reset" name="reset"> <span class="glyphicon glyphicon-remove " data-toggle="tooltip" data-placement="top" title="Reset" aria-hidden="true"></span></label></div></div><div class="form-group filtered-reply-count"><em class="control-label filter-count">15 / 15 replies shown</em></div></div></form></div><div class="invitations-container"><div class="invitation-buttons top-level-invitations"><span class="hint">Add:</span><button type="button" class="btn btn-xs  " data-id="NeurIPS.cc/2025/Conference/Submission15547/-/Withdrawal" data-toggle="tooltip" data-placement="top" title="" data-original-title="">Withdrawal</button><button type="button" class="btn btn-xs  " data-id="NeurIPS.cc/2025/Conference/Submission15547/-/Author_Final_Remarks" data-toggle="tooltip" data-placement="top" title="" data-original-title="">Author Final Remarks</button></div></div><div class="row forum-replies-container layout-default"><div class="col-xs-12"><div id="forum-replies"><div class="note  depth-odd" data-id="se5v31fhoo"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Official Review of Submission15547 by Reviewer ceRT</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=se5v31fhoo"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note se5v31fhoo"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 187, 187); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Review</span><span class="signatures">by <span>Reviewer ceRT</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>02 Jul 2025, 13:54 (modified: 24 Jul 2025, 16:36)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewer_ceRT"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer ceRT</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=se5v31fhoo">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Summary:</strong> <div class="note-content-value markdown-rendered"><p>The paper studies federated learning (FL) under system and data heterogeneity. Specifically, it explores how clients can learn from others, while preserving local model characteristics. This is a well-known problem, with several papers published on the topic in the past 2–3 years. The paper proposes an adaptive knowledge fusion method and employs selective knowledge distillation to address issues related to personalized knowledge loss in heterogeneous FL systems. Experimental results demonstrate the advantages of the proposed approach over several state-of-the-art methods. Additionally, the paper provides a convergence proof.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Strengths And Weaknesses:</strong> <div class="note-content-value markdown-rendered"><p>Strengths:</p>
<ul>
<li><p>The problem is timely, interesting, and important.</p>
</li>
<li><p>The paper is well written and easy to follow.</p>
</li>
<li><p>The experimental study is rigorous, supported by theoretical convergence analysis.</p>
</li>
<li><p>Resource overhead and time complexity are analyzed.</p>
</li>
</ul>
<p>Weaknesses:</p>
<ul>
<li><p>The problem and the proposed idea are not necessarily new, with several papers published on this topic in recent years.</p>
</li>
<li><p>The problem formulation in Section 3.1 is vague. The authors propose the optimization in Equation (1), but then state that they do not necessarily follow this objective. Could you then clarify what is meant by improving \theta_i? </p>
</li>
<li><p>What about transformer-based models? The paper does not clarify whether the proposed solution extends to transformer-based architectures or is limited to traditional (mainly CNN-based) models. There are also no experimental results for this. This, in my view, is a significant shortcoming.</p>
</li>
<li><p>How does device heterogeneity affect performance? I do not see any explicit discussion of this in the experimental section. In the experiments in Section 4, do the devices have the same computational and communication capabilities? The introduction mentions varying device resources, but this point is not revisited later.</p>
</li>
</ul>
</div></div><div><strong class="note-content-field disable-tex-rendering">Quality:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Clarity:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Significance:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Originality:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Questions:</strong> <div class="note-content-value markdown-rendered"><p>Please check the weakness section.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Limitations:</strong> <div class="note-content-value markdown-rendered"><p>Some are mentioned, but I was also expecting a discussion on whether using public data and knowledge distillation techniques, as proposed in this paper, could pose any privacy concerns.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Rating:</strong> <span class="note-content-value">4: Borderline accept: Technically solid paper where reasons to accept outweigh reasons to reject, e.g., limited evaluation. Please use sparingly.</span></div><div><strong class="note-content-field disable-tex-rendering">Confidence:</strong> <span class="note-content-value">4: You are confident in your assessment, but not absolutely certain. It is unlikely, but not impossible, that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work.</span></div><div><strong class="note-content-field disable-tex-rendering">Ethical Concerns:</strong> <span class="note-content-value">NO or VERY MINOR ethics concerns only</span></div><div><strong class="note-content-field disable-tex-rendering">Paper Formatting Concerns:</strong> <div class="note-content-value markdown-rendered"><p>NA.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Code Of Conduct Acknowledgement:</strong> <span class="note-content-value">Yes</span></div><div><strong class="note-content-field disable-tex-rendering">Responsible Reviewing Acknowledgement:</strong> <span class="note-content-value">Yes</span></div></div></div><div class="note-replies"><div class="note  depth-even" data-id="qjQi4t9ttl"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Rebuttal by Authors</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=qjQi4t9ttl"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note qjQi4t9ttl"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Rebuttal</span><span class="signatures">by <span>Authors (<span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to Conference, Authors"></span><a href="https://openreview.net/profile?id=~Huan_Huo1" target="_blank" rel="noreferrer">Huan Huo</a>, <a href="https://openreview.net/profile?id=~Yiran_Xiang1" target="_blank" rel="noreferrer">Yiran Xiang</a>, <a href="https://openreview.net/profile?id=~Guodong_Long1" target="_blank" rel="noreferrer">Guodong Long</a>, <a href="https://openreview.net/profile?id=~Xiufeng_Liu3" target="_blank" rel="noreferrer">Xiufeng Liu</a>, <a href="https://openreview.net/group/info?id=NeurIPS.cc/2025/Conference/Submission15547/Authors" target="_blank" rel="noreferrer">+3 more</a>)</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>30 Jul 2025, 15:38 (modified: 31 Jul 2025, 21:55)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=qjQi4t9ttl">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Rebuttal:</strong> <div class="note-content-value markdown-rendered"><p>We sincerely thank Reviewer ceRT for their time, thorough review, and constructive feedback on our submission. We appreciate their recognition of our paper’s timely and important problem, clear writing, rigorous experimental study, theoretical convergence
analysis, and resource overhead analysis.</p>
<p><strong>R1-1</strong>: The problem is not necessarily new, with several papers published on this topic in recent years.</p>
<p><strong>Re</strong>: We acknowledge that Heterogeneous Federated Learning is a vibrant and active research area, and indeed, many valuable works have been published recently. However, our contribution lies not in identifying the problem itself, but in proposing a novel and highly effective solution that addresses its core challenges in a unique and synergistic way. To the best of our knowledge, Fedfuse is the first framework to integrate <strong>dynamic expert-guided fusion</strong> with a <strong>compatible selective logit-based distillation</strong> in the context of heterogeneous federated learning. This specific combination directly tackles key limitations in existing methods:</p>
<ol>
<li><strong>Dynamic Personalized Fusion:</strong> In contrast to previous HeteroFL methods that often rely on static aggregation schemes or simple averaging, Fedfuse employs a novel server-side Expert-guided Fusion (EGF) mechanism via a Mixture-of-Experts (MoE) operating on client logits. This dynamically gates and weights heterogeneous client knowledge contributions (lines 8-12, 58-65), adapting to architectural and statistical heterogeneity. This adaptive fusion mechanism is crucial for preserving personalized knowledge during aggregation, which is not effectively supported by existing heterogeneous FL methods.</li>
<li><strong>Selective Distillation:</strong> While knowledge distillation has been explored in FL, existing HeteroFL approaches do not perform a truly selective distillation that is compatible with MoE-based adaptive fusion. Fedfuse introduces an elaborately designed selective knowledge distillation (SKD) strategy using reverse KL divergence. This mechanism allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence (lines 48-53), addressing personalized knowledge loss more effectively.
It is this unique synergy and the effective design choices in integrating these components that constitute the primary novelty of Fedfuse and lead to the superior empirical performance shown in our results (Table 1). We propose strengthening the introduction (Section 1) and related work (Section 2) to explicitly highlight these unique contributions and how they advance the state-of-the-art beyond existing methods.</li>
</ol>
<p><strong>R1-2</strong>: The problem formulation in Section 3.1 is vague. The authors propose the optimization in Equation (1), but then state that they do not necessarily follow this objective. Could you then clarify what is meant by improving <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="0" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container>?</p>
<p><strong>Re</strong>: We appreciate this important question, which highlights a point that requires clarification for better understanding. Equation (1) (lines 125-127) formulates the overarching \textbf{personalized objective} for each client (minimizing their local loss <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="1" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D439 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em; margin-left: -0.106em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>F</mi><mi>i</mi></msub><mo stretchy="false">(</mo><msub><mi>θ</mi><mi>i</mi></msub><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container> over their private dataset), which is the primary goal of personalized federated learning. FedFuse does not directly optimize the aggregate sum in Equation (1) through a global gradient descent step as in traditional FL. Instead, FedFuse's mechanisms--Expert-guided Fusion (EGF) at the server and Selective Knowledge Distillation (SKD) at the clients--are meticulously designed to \textbf{facilitate knowledge exchange} (lines 130-131) between clients and the server.
In practice, this is achieved through three explicit sub-objectives: 1) <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="2" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>c</mi></msub></math></mjx-assistive-mml></mjx-container> (Sec. 3.3) is the cross-entropy loss used for local training on individual clients. 2) <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="3" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>a</mi></msub></math></mjx-assistive-mml></mjx-container> (Sec. 3.4) is the server-side aggregation loss that guides expert fusion. 3).<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="4" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container> (Sec. 3.5) is the personalization loss applied on clients, leveraging filtered global knowledge.
The overall improvement of <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="5" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container> towards its personalized objective (Equation 1) emerges through the sequential coordination of these objectives. Specifically, local parameters <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="6" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container> are first updated via <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="7" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>c</mi></msub></math></mjx-assistive-mml></mjx-container> on each client <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="8" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mo class="mjx-n"><mjx-c class="mjx-c2192"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mo stretchy="false">→</mo></math></mjx-assistive-mml></mjx-container> the server then computes <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="9" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>a</mi></msub></math></mjx-assistive-mml></mjx-container> to optimize the expert fusion process <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="10" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mo class="mjx-n"><mjx-c class="mjx-c2192"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mo stretchy="false">→</mo></math></mjx-assistive-mml></mjx-container> finally, clients apply <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="11" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container> to further personalize <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="12" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container> using selectively distilled global knowledge. This structured optimization pipeline ensures that <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="13" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container> maintains a balance between local task performance and global representational compatibility, as elaborated in Algorithm 1 (lines 12–16) and Algorithm 2. In essence, FedFuse provides an adaptive and personalized \textit{guidance mechanism} that assists each client in converging towards its optimal personalized model, which aligns with the spirit of minimizing the objective in Equation (1) on a per-client basis. We will add a clarifying sentence in Section 3.1, perhaps after Equation (1), to explicitly state: "While this equation represents the ideal personalized objective, FedFuse's framework focuses on designing novel mechanisms for knowledge exchange that <strong>enable each client's local model (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="14" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>θ</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container>) to more effectively minimize its individual <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="15" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D439 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em; margin-left: -0.106em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D703 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>F</mi><mi>i</mi></msub><mo stretchy="false">(</mo><msub><mi>θ</mi><mi>i</mi></msub><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container>}</strong>, thereby achieving this personalized goal without directly optimizing the aggregate sum."</p>
<p><strong>R1-3</strong>: What about transformer-based models? The paper does not clarify whether the proposed solution extends to transformer-based architectures or is limited to traditional (mainly CNN-based) models. There are also no experimental results for this. This, in my view, is a significant shortcoming.</p>
<p><strong>Re</strong>:We have expanded our experiments. The complete experimental results and model configurations are systematically presented in Tables below. 
FedFuse is fundamentally architecture-agnostic due to its dual design: </p>
<ol>
<li>Logits-level operations—including MoE-based fusion and selective distillation—are decoupled from model architectures. Dynamic gating operates on feature representations and naturally adapts to different architectures, whether based on CNN activations or Transformer embeddings. </li>
<li>Selective distillation depends solely on the output distribution (logits), rather than architecture-specific internal representations, ensuring compatibility across diverse model backbones.
<strong>Table 1: Model Configurations</strong></li>
</ol>
<h3>Model Configures</h3>
<table>
<thead>
<tr>
<th>Model Name</th>
<th>Model Architecture</th>
<th>Key Features</th>
<th>Params.</th>
</tr>
</thead>
<tbody><tr>
<td>Client Model_1</td>
<td>Transformer</td>
<td>1-head self-attention, 1 FFN</td>
<td>4.12M</td>
</tr>
<tr>
<td>Client Model_2</td>
<td>Transformer</td>
<td>2-head self-attention, 2 FFN</td>
<td>4.14M</td>
</tr>
<tr>
<td>Client Model_3</td>
<td>Transformer</td>
<td>4-head self-attention, 4 FFN</td>
<td>4.18M</td>
</tr>
<tr>
<td>Client Model_4</td>
<td>Transformer</td>
<td>8-head self-attention, 8 FFN</td>
<td>4.26M</td>
</tr>
<tr>
<td>Client Model_5</td>
<td>Transformer</td>
<td>16-head self-attention, 16 FFN</td>
<td>4.42M</td>
</tr>
</tbody></table>
<p><strong>Table 2: Results on AGNews (50 clients)</strong></p>
<table>
<thead>
<tr>
<th>Algo</th>
<th>Ours</th>
<th>FedKD</th>
<th>FedProto</th>
<th>FedMRL</th>
<th>FedTGP</th>
</tr>
</thead>
<tbody><tr>
<td>Acc(%)</td>
<td>96.11 (23.73%<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="16" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mo class="mjx-n"><mjx-c class="mjx-c2191"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mo stretchy="false">↑</mo></math></mjx-assistive-mml></mjx-container>)</td>
<td>94.80</td>
<td>77.68</td>
<td>94.82</td>
<td>94.76</td>
</tr>
</tbody></table>
<p><strong>R1-4</strong>: How does device heterogeneity affect performance? I do not see any explicit discussion of this in the experimental section. In the experiments in Section 4, do the devices have the same computational and communication capabilities? The introduction mentions varying device resources, but this point is not revisited later.</p>
<p><strong>Re</strong>: Due to experimental environment constraints, we have not yet deployed FedFuse on real-world heterogeneous devices for testing. However, we have validated the impact of device heterogeneity through the following designs:</p>
<ol>
<li>Architectural heterogeneity serves as a direct proxy for device resource heterogeneity: The variation in client model parameters (column Param. in Table 7 in Appendix) precisely simulates different devices' computational/memory constraints.</li>
<li>Parameter Scale Difference Testing: Results demonstrate stable accuracy maintenance despite significant parameter quantity differences in Table below.</li>
</ol>
<p><strong>Table: Parameter-Accuracy Results (Flowers102, 10 clients, Dir α=0.1)</strong></p>
<table>
<thead>
<tr>
<th>Client ID</th>
<th>1</th>
<th>2</th>
<th>3</th>
<th>4</th>
<th>5</th>
<th>6</th>
<th>7</th>
<th>8</th>
<th>9</th>
<th>10</th>
</tr>
</thead>
<tbody><tr>
<td>Model Param. (M)</td>
<td>3.2</td>
<td>11.2</td>
<td>21.3</td>
<td>23.5</td>
<td>42.5</td>
<td>58.2</td>
<td>6.8</td>
<td>3.5</td>
<td>3.2</td>
<td>11.2</td>
</tr>
<tr>
<td>Accuracy(%)</td>
<td>54.55</td>
<td>50.74</td>
<td>62.78</td>
<td>52.08</td>
<td>59.59</td>
<td>50.00</td>
<td>47.03</td>
<td>47.93</td>
<td>51.55</td>
<td>48.44</td>
</tr>
</tbody></table>
<p><strong>R1-5</strong>: Some are mentioned, but I was also expecting a discussion on whether using public data and knowledge distillation techniques, as proposed in this paper, could pose any privacy concerns.</p>
<p><strong>Re</strong>: We appreciate your highlighting of privacy concerns as a critical consideration. FedFuse incorporates several design choices that inherently enhance privacy protection:</p>
<ol>
<li>Server-side isolation: The public dataset is used exclusively to train the MoE gating network on the server side, without any access to client data. Moreover, only final-layer logits are aggregated—raw inputs and internal features remain strictly on the client side. </li>
<li>Minimal exposure of information: Clients share only final-layer logits, rather than gradients or intermediate features, substantially reducing the risk of information leakage when compared to traditional FL approaches.</li>
</ol>
</div></div></div></div></div><div class="note  depth-even" data-id="wyIvRlKKid"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Rebuttal by Authors</h5></div><div class="heading"><h4><span>Mandatory Acknowledgement by Reviewer ceRT</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=wyIvRlKKid"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note wyIvRlKKid"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Mandatory Acknowledgement</span><span class="signatures">by <span>Reviewer ceRT</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>04 Aug 2025, 11:55</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Mandatory Acknowledgement:</strong> <span class="note-content-value">I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. <a rel="noopener noreferrer" href="https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/" target="_blank">https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/</a></span></div></div></div></div><div class="note  depth-even" data-id="PiFXGHZhhe"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Mandatory Acknowledgement by Reviewer ceRT</h5></div><div class="heading"><h4><span>Official Comment by Reviewer ceRT</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=PiFXGHZhhe"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note PiFXGHZhhe"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(187, 187, 255); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Comment</span><span class="signatures">by <span>Reviewer ceRT</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>04 Aug 2025, 12:01</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Comment:</strong> <div class="note-content-value markdown-rendered"><p>Thanks for the reply, especially for the new results on transformers and heterogeneous settings. Please consider adding these results to the paper. Regarding the problem formulation, I am not yet convinced by your response. If you do not directly optimize equation (1), then it should not be mentioned in the paper.</p>
<p>I am keeping my score, as I believe it fairly reflects the contribution of the paper.</p>
</div></div></div></div></div><div class="note  depth-even" data-id="m39kU4IMCz"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><strong>Thanks for your concerns</strong></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=m39kU4IMCz"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note m39kU4IMCz"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(187, 187, 255); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Comment</span><span class="signatures">by <span>Authors (<span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to Conference, Authors"></span><a href="https://openreview.net/profile?id=~Huan_Huo1" target="_blank" rel="noreferrer">Huan Huo</a>, <a href="https://openreview.net/profile?id=~Yiran_Xiang1" target="_blank" rel="noreferrer">Yiran Xiang</a>, <a href="https://openreview.net/profile?id=~Guodong_Long1" target="_blank" rel="noreferrer">Guodong Long</a>, <a href="https://openreview.net/profile?id=~Xiufeng_Liu3" target="_blank" rel="noreferrer">Xiufeng Liu</a>, <a href="https://openreview.net/group/info?id=NeurIPS.cc/2025/Conference/Submission15547/Authors" target="_blank" rel="noreferrer">+3 more</a>)</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>06 Aug 2025, 06:18 (modified: 06 Aug 2025, 06:29)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=m39kU4IMCz">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Comment:</strong> <div class="note-content-value markdown-rendered"><p>We sincerely appreciate your valuable comments and acknowledge our rebuttal. In our manuscript, Equation (1) is the overall objective function of the whole framework, which is necessary. The following losses (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="17" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c2C"></mjx-c></mjx-mo><mjx-msub space="2"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c2C"></mjx-c></mjx-mo><mjx-msub space="2"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>c</mi></msub><mo>,</mo><msub><mi>L</mi><mi>a</mi></msub><mo>,</mo><msub><mi>L</mi><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container>) are the decoupling of Equation (1), which are the sub-objective of the overall function.
We will reorganize the Equation (1) as follows in the next version.</p>
<p><mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" display="true" justify="left" tabindex="0" ctxtmenu_counter="18" style="font-size: 121.3%; position: relative;"><mjx-math display="true" class="MJX-TEX" aria-hidden="true" style="margin-left: 0px;"><mjx-mo class="mjx-n"><mjx-c class="mjx-c6D"></mjx-c><mjx-c class="mjx-c69"></mjx-c><mjx-c class="mjx-c6E"></mjx-c></mjx-mo><mjx-munderover space="2"><mjx-over style="padding-bottom: 0.2em; padding-left: 0.408em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi></mjx-over><mjx-box><mjx-munder><mjx-row><mjx-base><mjx-mo class="mjx-lop"><mjx-c class="mjx-c2211 TEX-S2"></mjx-c></mjx-mo></mjx-base></mjx-row><mjx-row><mjx-under style="padding-top: 0.167em; padding-left: 0.148em;"><mjx-texatom size="s" texclass="ORD"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n"><mjx-c class="mjx-c31"></mjx-c></mjx-mn></mjx-texatom></mjx-under></mjx-row></mjx-munder></mjx-box></mjx-munderover><mjx-msub space="2"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-texatom texclass="ORD"><mjx-mi class="mjx-ds mjx-b"><mjx-c class="mjx-c1D53C TEX-A"></mjx-c></mjx-mi></mjx-texatom><mjx-mo class="mjx-n"><mjx-c class="mjx-c5B"></mjx-c></mjx-mo><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D440 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em; margin-left: -0.081em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D465 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo><mjx-mo class="mjx-n"><mjx-c class="mjx-c2C"></mjx-c></mjx-mo><mjx-mi class="mjx-i" space="2"><mjx-c class="mjx-c1D466 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo><mjx-mo class="mjx-n"><mjx-c class="mjx-c5D"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="block"><math xmlns="http://www.w3.org/1998/Math/MathML" display="block"><mo data-mjx-texclass="OP" movablelimits="true">min</mo><munderover><mo data-mjx-texclass="OP">∑</mo><mrow data-mjx-texclass="ORD"><mi>i</mi><mo>=</mo><mn>1</mn></mrow><mi>K</mi></munderover><msub><mi>p</mi><mi>i</mi></msub><mrow data-mjx-texclass="ORD"><mi mathvariant="double-struck">E</mi></mrow><mo stretchy="false">[</mo><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mo stretchy="false">(</mo><msub><mi>M</mi><mi>i</mi></msub><mo stretchy="false">(</mo><mi>x</mi><mo stretchy="false">)</mo><mo>,</mo><mi>y</mi><mo stretchy="false">)</mo><mo stretchy="false">]</mo></math></mjx-assistive-mml></mjx-container>  </p>
<p>where the composite loss <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="19" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow></math></mjx-assistive-mml></mjx-container> combines <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="20" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>c</mi></msub></math></mjx-assistive-mml></mjx-container>,  <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="21" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>a</mi></msub></math></mjx-assistive-mml></mjx-container>, and <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="22" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container>.</p>
<p><mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" display="true" justify="left" tabindex="0" ctxtmenu_counter="23" style="font-size: 121.3%; position: relative;"><mjx-math display="true" class="MJX-TEX" aria-hidden="true" style="margin-left: 0px;"><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-msub space="4"><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n" space="3"><mjx-c class="mjx-c2B"></mjx-c></mjx-mo><mjx-msub space="3"><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub><mjx-mo class="mjx-n" space="3"><mjx-c class="mjx-c2B"></mjx-c></mjx-mo><mjx-msub space="3"><mjx-texatom texclass="ORD"><mjx-mi class="mjx-cal mjx-i"><mjx-c class="mjx-c4C TEX-C"></mjx-c></mjx-mi></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="block"><math xmlns="http://www.w3.org/1998/Math/MathML" display="block"><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mo>=</mo><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>c</mi></msub><mo>+</mo><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>a</mi></msub><mo>+</mo><msub><mrow data-mjx-texclass="ORD"><mi data-mjx-variant="-tex-calligraphic" mathvariant="script">L</mi></mrow><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container></p>
</div></div></div></div></div></div></div><div class="note  depth-odd" data-id="T1Rzu5OfBG"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Official Review of Submission15547 by Reviewer kjvn</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=T1Rzu5OfBG"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note T1Rzu5OfBG"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 187, 187); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Review</span><span class="signatures">by <span>Reviewer kjvn</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>02 Jul 2025, 12:16 (modified: 05 Aug 2025, 09:33)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewer_kjvn"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer kjvn</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=T1Rzu5OfBG">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Summary:</strong> <div class="note-content-value markdown-rendered"><p>The paper proposes FedFuse, a framework that addresses personalized knowledge loss in heterogeneous federated learning (HeteroFL). It introduces a Mixture-of-Experts (MoE) based fusion mechanism at the server and a selective knowledge distillation method at the client side. The design aims to better preserve personalization and robustness under high statistical and architectural heterogeneity. Experimental results demonstrate notable improvements over several baselines across datasets and client scales.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Strengths And Weaknesses:</strong> <div class="note-content-value markdown-rendered"><p>Strengths</p>
<p>1: The combination of MoE and selective distillation is a non-trivial architectural design and aligns well with the HeteroFL challenges.
2: Empirical results are extensive and include large-scale evaluations, ablations, and comparisons with strong baselines.
3: The convergence theory and complexity analysis are more thorough than typical HeteroFL papers.</p>
<p>Weaknesses</p>
<p>The MoE design at the server introduces considerable complexity, but its cost-benefit tradeoff is not convincingly discussed. While the server time is reported to be lower than some baselines, it is unclear whether this would hold in larger models or in practical deployments.</p>
<p>The role of the public dataset is pivotal in both training and distillation, but the paper does not discuss what happens when such data is scarce or misaligned with client distributions.</p>
<p>The method assumes availability of accurate logits from diverse architectures, yet it is unclear how logit quality differences (due to architectural disparities) affect MoE training. This issue is only indirectly touched in ablations and not deeply investigated.</p>
<p>While the distillation strategy uses reverse KL divergence to preserve personalization, it’s unclear how it behaves when the global logits are overconfident or undercalibrated. No analysis on calibration or temperature tuning impact is given.</p>
<p>The contribution claims are somewhat overstated in comparison to the originality of the components. MoE and selective distillation are known ideas; the innovation lies more in the integration rather than in novel algorithmic development.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Quality:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Clarity:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Significance:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Originality:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Questions:</strong> <div class="note-content-value markdown-rendered"><p>What is the behavior of the method when the public dataset is very small or unrepresentative of the clients’ private data? Could you quantify the degradation or propose adaptations?</p>
<p>Have you tested the sensitivity of performance to the number of experts in the MoE or the top-k parameter? Could you include a visualization of these hyperparameter effects?</p>
<p>How do you ensure that the logits coming from drastically different architectures are equally informative? Would a calibration step help in balancing their influence on the MoE?</p>
<p>Why was reverse KL selected for the personalization objective? Would forward KL or other divergence measures (e.g., JSD or cosine distance) yield similar results?</p>
<p>Could you elaborate on the failure cases or where FedFuse underperforms? The current narrative only emphasizes the wins, which weakens the practical utility assessment.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Limitations:</strong> <div class="note-content-value markdown-rendered"><p>The reliance on a high-quality public dataset limits the method’s deployability in privacy-critical or domain-specific settings.</p>
<p>FedFuse assumes that clients can reliably compute logits over a shared dataset, which might not be feasible in constrained environments or for non-vision tasks.</p>
<p>The scalability in real-world resource-constrained edge environments is not validated beyond simulation.</p>
<p>The generalizability beyond image classification (e.g., to NLP or multimodal tasks) is not discussed.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Confidence:</strong> <span class="note-content-value">4: You are confident in your assessment, but not absolutely certain. It is unlikely, but not impossible, that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work.</span></div><div><strong class="note-content-field disable-tex-rendering">Ethical Concerns:</strong> <span class="note-content-value">NO or VERY MINOR ethics concerns only</span></div><div><strong class="note-content-field disable-tex-rendering">Paper Formatting Concerns:</strong> <div class="note-content-value markdown-rendered"><p>na</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Code Of Conduct Acknowledgement:</strong> <span class="note-content-value">Yes</span></div><div><strong class="note-content-field disable-tex-rendering">Responsible Reviewing Acknowledgement:</strong> <span class="note-content-value">Yes</span></div></div></div><div class="note-replies"><div class="note  depth-even" data-id="s6UHAmIPvH"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Rebuttal by Authors</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=s6UHAmIPvH"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note s6UHAmIPvH"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Rebuttal</span><span class="signatures">by <span>Authors (<span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to Conference, Authors"></span><a href="https://openreview.net/profile?id=~Huan_Huo1" target="_blank" rel="noreferrer">Huan Huo</a>, <a href="https://openreview.net/profile?id=~Yiran_Xiang1" target="_blank" rel="noreferrer">Yiran Xiang</a>, <a href="https://openreview.net/profile?id=~Guodong_Long1" target="_blank" rel="noreferrer">Guodong Long</a>, <a href="https://openreview.net/profile?id=~Xiufeng_Liu3" target="_blank" rel="noreferrer">Xiufeng Liu</a>, <a href="https://openreview.net/group/info?id=NeurIPS.cc/2025/Conference/Submission15547/Authors" target="_blank" rel="noreferrer">+3 more</a>)</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>30 Jul 2025, 17:21 (modified: 31 Jul 2025, 21:55)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=s6UHAmIPvH">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Rebuttal:</strong> <div class="note-content-value markdown-rendered"><p>We sincerely thank Reviewer kjvn for their time, thorough review, and constructive feedback on our submission. 
<strong>R2-1</strong>: What is the behavior of the method when the public dataset is very small or unrepresentative of the clients’ private data? Could you quantify the degradation or propose adaptations?</p>
<p><strong>Re</strong>: We thank the reviewer for raising this important point regarding the crucial role of the public dataset. In FedFuse, the public dataset <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="24" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> (lines 134-137) serves as a common reference for knowledge representation and fusion, which is vital for both expert-guided fusion and selective knowledge distillation.
While it is not used to model client-specific distributions directly, <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="25" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> is fundamental for clients to generate comparable logits and for the server's MoE to synthesize effective global knowledge.
As such, if <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="26" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> is severely scarce or significantly misaligned with client data distributions, performance would indeed degrade. This is because the quality and relevance of the fused global knowledge would be compromised, making it harder for clients to learn effectively.
For such extreme scenarios where public data is severely unrepresentative, we can propose an adaptation called <strong>dynamic expert pruning</strong>. This adaptation could allow the system to automatically down-weight or remove underutilized experts when the gating signals become unreliable due to poor public data coverage or when an expert consistently produces low-quality or irrelevant logits. This mechanism would help mitigate the negative impact of a very poor <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="27" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container>.</p>
<p><strong>R2-2</strong>: Have you tested the sensitivity of performance to the number of experts in the MoE or the top-k parameter? Could you include a visualization of these hyperparameter effects?</p>
<p><strong>Re</strong>: We indeed conducted a sensitivity analysis on the MoE hyperparameters, including the number of experts (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="28" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi></math></mjx-assistive-mml></mjx-container>) and the top-<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="29" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> parameter. Detailed visualizations of these effects are provided in Appendix (Figure 5).
Our setting for the total number of experts (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="30" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi></math></mjx-assistive-mml></mjx-container>, lines 623-624) is proportional to the number of clients (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="31" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>K</mi></math></mjx-assistive-mml></mjx-container>), which allows for sufficient specialization, as each client can, in theory, be routed to a dedicated expert. To ensure computational and memory efficiency, our gating network adopts a top-<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="32" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> sparse routing strategy (lines 181-183), where only a subset of experts (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="33" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container>, lines 623-624) is activated for each client in each round. As illustrated in Figure 5 (Appendix), activating around 40% of the total experts per round (i.e., <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="34" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c30"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c34"></mjx-c></mjx-mn><mjx-mo class="mjx-n" space="3"><mjx-c class="mjx-cD7"></mjx-c></mjx-mo><mjx-mi class="mjx-i" space="3"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi><mo>=</mo><mn>0.4</mn><mo>×</mo><mi>E</mi></math></mjx-assistive-mml></mjx-container>) achieves the best trade-off between performance and efficiency. This balances the need for personalized capacity with controlled computational overhead. We will expand on this analysis in Appendix E.2, providing a more detailed visualization and discussion of the sensitivity for <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="35" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi></math></mjx-assistive-mml></mjx-container> and <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="36" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container>. </p>
<p><strong>R2-3</strong>: How do you ensure that the logits coming from drastically different architectures are equally informative? Would a calibration step help in balancing their influence on the MoE?</p>
<p><strong>Re</strong>: FedFuse employs a dual-mechanism approach to handle logit comparability while accommodating the MoE's heterogeneous logit processing:</p>
<ol>
<li>Temperature Scaling and Normalization: Client logits are processed through temperature scaling (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="37" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c32"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi><mo>=</mo><mn>2.0</mn></math></mjx-assistive-mml></mjx-container>, lines 169, 622) and followed by normalization (Equation 4) to mitigate issues of overconfidence or undercalibration and eliminate dimensional discrepancies. This prepares the logits for more effective fusion.</li>
<li>Dynamic Expert Weighting: The core mechanism for handling varying logit "informativeness" is the MoE's gating network. This network (lines 44-46, 114-115) is specifically designed to adaptively weight and route contributions from diverse clients. If a client's logits are less informative or misaligned (e.g., from a smaller model or one less suited to a particular public data sample), the gating network will implicitly learn to assign lower routing weights to it for relevant experts, or route it to experts better suited to handle such representations. This adaptive weighting and routing is a core advantage of MoE over simpler aggregation methods, which would treat all contributions equally, potentially amplifying noise from lower-quality logits. Thus, MoE acts as an inherent filter and balancer, mitigating the negative impact of such differences.</li>
</ol>
<p>Our empirical success under high architectural heterogeneity (Table 1) implicitly validates this adaptive handling of varying logit qualities. We propose adding a sentence in Section 3.4 ("Expert-guided Fusion") to clarify this: "The gating network within the MoE mechanism is inherently designed to <strong>adaptively weigh and route contributions from diverse clients</strong>, implicitly accommodating variations in logit quality due to architectural disparities and mitigating the negative impact of less informative logits by assigning lower weights or routing them to more appropriate experts."</p>
<p><strong>R2-4</strong>: Why was reverse KL selected for the personalization objective? Would forward KL or other divergence measures (e.g., JSD or cosine distance) yield similar results?</p>
<p><strong>Re</strong>: We employ reverse KL divergence (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="38" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>D</mi><mi>K</mi><mo stretchy="false">(</mo><mi>Q</mi><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mi>P</mi><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container>) for the personalization objective (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="39" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45D TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>p</mi></msub></math></mjx-assistive-mml></mjx-container>, lines 208-212) due to its distinctive properties, which are crucial for preserving client-specific modes.</p>
<ol>
<li>Theoretical Rationale for Personalization (Line 208): Minimizing <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="40" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>D</mi><mi>K</mi><mo stretchy="false">(</mo><mi>Q</mi><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mi>P</mi><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container> (where <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="41" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> is global <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="42" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi></msub></math></mjx-assistive-mml></mjx-container> and <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="43" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> is client <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="44" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>c</mi><mi>i</mi></msub></math></mjx-assistive-mml></mjx-container>) encourages <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="45" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> to have high probability where <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="46" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> has high probability. However, if <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="47" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> has low probability (or zero), <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="48" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> is <strong>not penalized</strong> for having high probability there. This asymmetric property allows <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="49" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> (the client model) to <strong>retain its "personal modes"</strong> or unique features that might not be strongly represented in the global model, thereby preserving client-specific knowledge without blind imitation. This is critical for preventing the global model from forcing "overconfident" or "miscalibrated" modes onto clients. The temperature parameter <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="50" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> (lines 169, 190, 622) further softens distributions, implicitly acting as a calibration step.</li>
<li>Empirical Validation: We conducted experiments comparing reverse KL with other divergence measures, as shown in Table below. Our results indicate that other divergence measures (JSD, Cosine Distance) fail to achieve comparable performance to reverse KL divergence, underscoring its effectiveness in maintaining personalization while integrating global knowledge.</li>
</ol>
<p>We will add a sentence in Section 3.5 ("Selective Knowledge Distillation") to explicitly state that <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="51" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> addresses calibration and that reverse KL's property is key for personalization. We will also suggest future work on more extensive temperature tuning analysis.</p>
<p><strong>Table: Results of different divergences (CIFAR-100, Dir α=0.1, 10 clients)</strong></p>
<table>
<thead>
<tr>
<th>Divergence Measure</th>
<th>Accuracy (%)</th>
</tr>
</thead>
<tbody><tr>
<td><strong>Ours</strong></td>
<td>42.22</td>
</tr>
<tr>
<td>JS</td>
<td>40.34</td>
</tr>
<tr>
<td>Cosine</td>
<td>36.52</td>
</tr>
</tbody></table>
<p><strong>R2-6</strong>: The MoE design at the server introduces considerable complexity, but its cost-benefit tradeoff is not convincingly discussed. While the server time is reported to be lower than some baselines, it is unclear whether this would hold in larger models or in practical deployments.</p>
<p><strong>Re</strong>: We have conducted additional quantitative analysis comparing our method (Ours) with its ablated version that removes the MoE component (denoted as w/o MoE). The FLOPs incurred by the MoE module are modest, largely due to our use of a top-k routing mechanism that activates only a subset of experts per forward pass. Memory consumption remains well within feasible limits, even with increasing model size or expert count.</p>
<h3>Results (CIFAR-100, Dir α=0.1, 50 clients)</h3>
<table>
<thead>
<tr>
<th>Method</th>
<th>FLOPS</th>
<th>Memory</th>
</tr>
</thead>
<tbody><tr>
<td><strong>FedFuse</strong></td>
<td>100G</td>
<td>808.08MB</td>
</tr>
<tr>
<td>w/o MoE</td>
<td>99.1G</td>
<td>726.41MB</td>
</tr>
</tbody></table>
<p><em>Table: Results (CIFAR-100, Dir α=0.1, 50 clients).</em></p>
<table>
<thead>
<tr>
<th>Client Numbers</th>
<th>10</th>
<th>20</th>
<th>50</th>
<th>100</th>
<th>200</th>
<th>500</th>
</tr>
</thead>
<tbody><tr>
<td>Memory (MB)</td>
<td>680.28</td>
<td>707.23</td>
<td>808.08</td>
<td>864.95</td>
<td>902.43</td>
<td>1958.63</td>
</tr>
</tbody></table>
<p><strong>R2-7</strong>: While the distillation strategy uses reverse KL divergence to preserve personalization, it’s unclear how it behaves when the global logits are overconfident or undercalibrated. No analysis on calibration or temperature tuning impact is given.</p>
<p><strong>Re</strong>:To address this, we have conducted an additional sensitivity analysis on the distillation temperature parameter <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="52" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> used in the reverse KL divergence loss. Specifically, we vary <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="53" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> in a reasonable range e.g.,0.5,1.0,2.0,5.0 and measure the resulting performance across multiple client models.</p>
<p>The performance of our method remains stable over a broad range of <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="54" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> values, indicating robustness to calibration variations. When <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="55" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> is set to a moderate value (e.g., <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="56" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c32"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi><mo>=</mo><mn>2.0</mn></math></mjx-assistive-mml></mjx-container>), the global logits are sufficiently softened, reducing the impact of overconfidence and improving the alignment with personalized local distributions. Lower <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="57" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> values tend to retain sharper (and potentially overconfident) global predictions, slightly degrading performance, while excessively high <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="58" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> values dilute the knowledge transfer.</p>
<p>These findings suggest that our reverse KL-based distillation mechanism is not overly sensitive to global model calibration, and that appropriate temperature tuning (e.g., <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="59" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c2208"></mjx-c></mjx-mo><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c5B"></mjx-c></mjx-mo><mjx-mn class="mjx-n"><mjx-c class="mjx-c31"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn><mjx-mo class="mjx-n"><mjx-c class="mjx-c2C"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="2"><mjx-c class="mjx-c32"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn><mjx-mo class="mjx-n"><mjx-c class="mjx-c5D"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi><mo>∈</mo><mo stretchy="false">[</mo><mn>1.0</mn><mo>,</mo><mn>2.0</mn><mo stretchy="false">]</mo></math></mjx-assistive-mml></mjx-container>) can mitigate overconfidence issues in global logits.</p>
<h3>Different Temperatures Results (CIFAR-100, 10 clients)</h3>
<table>
<thead>
<tr>
<th>Temperature</th>
<th>0.5</th>
<th>1</th>
<th>2</th>
<th>3</th>
<th>4</th>
<th>5</th>
</tr>
</thead>
<tbody><tr>
<td>Accuracy(%)</td>
<td>33.56</td>
<td>40.35</td>
<td>42.22</td>
<td>39.22</td>
<td>38.84</td>
<td>34.75</td>
</tr>
</tbody></table>
</div></div></div></div></div><div class="note  depth-even" data-id="NoSFFSKjbS"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Rebuttal by Authors</h5></div><div class="heading"><h4><span>Mandatory Acknowledgement by Reviewer kjvn</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=NoSFFSKjbS"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note NoSFFSKjbS"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Mandatory Acknowledgement</span><span class="signatures">by <span>Reviewer kjvn</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>05 Aug 2025, 09:31</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Mandatory Acknowledgement:</strong> <span class="note-content-value">I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. <a rel="noopener noreferrer" href="https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/" target="_blank">https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/</a></span></div></div></div></div><div class="note  depth-even" data-id="gZu67fQl4O"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Mandatory Acknowledgement by Reviewer kjvn</h5></div><div class="heading"><h4><span>Official Comment by Reviewer kjvn</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=gZu67fQl4O"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note gZu67fQl4O"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(187, 187, 255); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Comment</span><span class="signatures">by <span>Reviewer kjvn</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>05 Aug 2025, 09:34</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Comment:</strong> <div class="note-content-value markdown-rendered"><p>The authors rebuttal resolved part of my questions. I'm not 100% satisfied, but I believe 4 is a reasonable score for the authors effort.</p>
</div></div></div></div></div></div></div><div class="note  depth-odd" data-id="khUJd1bUO7"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Official Review of Submission15547 by Reviewer r41q</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=khUJd1bUO7"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note khUJd1bUO7"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 187, 187); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Review</span><span class="signatures">by <span>Reviewer r41q</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>02 Jul 2025, 05:15 (modified: 24 Jul 2025, 16:36)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewer_r41q"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer r41q</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=khUJd1bUO7">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Summary:</strong> <div class="note-content-value markdown-rendered"><p>This paper proposes FedFuse for heterogeneous federated learning, to address two crucial issues：the loss of personalized knowledge during aggregation and the risk of disrupting locally learned features. Specifically, FedFuse introduces an expert-guided fusion mechanism to dynamically capture and fuse personalized knowledge from heterogeneous clients; besides, a selective knowledge distillation strategy comes up to absorb global knowledge selectively rather than blindly, to preserve key local model features. Extensive experiments verify the effectiveness of FedFuse over state-of-the-art HeteroFL baselines.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Strengths And Weaknesses:</strong> <div class="note-content-value markdown-rendered"><p>Strengths:</p>
<ol>
<li>Novelty methodology:</li>
</ol>
<p>-- FedFuse dynamically gates and weights client logits contributions via an MoE mechanism, to produce an adaptive aggregation.</p>
<p>-- FedFuse introduces an alignment-based criterion for selective distillation, balancing global consensus with local specialization.</p>
<ol start="2">
<li>Comprehensive Experimental Evaluation:</li>
</ol>
<p>-- Large‐scale experiments (500 clients), multiple heterogeneity configurations, and ablation studies convincingly demonstrate the necessity and effectiveness of the core components.</p>
<p>Weaknesses:</p>
<p>Some concerns are listed below:</p>
<p>For the selective distillation mechanism, although reverse KL is adopted to preserve client-specific modes, it is unclear why and how Q with low probability can indicate the personal modes of P. Intuitively, it should be determined by the clients which parts are most relevant to them. Besides, it is not clarified whether all logits in <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="60" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.247em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.29em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container> are equally distilled or filtered. Could adopting more aggressive filtering strategy further preserve personalization? The authors should provide detailed explanation and convincing experimental validation.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Quality:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Clarity:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Significance:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Originality:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Questions:</strong> <div class="note-content-value markdown-rendered"><p>Please answer the above mentioned questions.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Limitations:</strong> <div class="note-content-value markdown-rendered"><p>yes</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Rating:</strong> <span class="note-content-value">4: Borderline accept: Technically solid paper where reasons to accept outweigh reasons to reject, e.g., limited evaluation. Please use sparingly.</span></div><div><strong class="note-content-field disable-tex-rendering">Confidence:</strong> <span class="note-content-value">4: You are confident in your assessment, but not absolutely certain. It is unlikely, but not impossible, that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work.</span></div><div><strong class="note-content-field disable-tex-rendering">Ethical Concerns:</strong> <span class="note-content-value">NO or VERY MINOR ethics concerns only</span></div><div><strong class="note-content-field disable-tex-rendering">Paper Formatting Concerns:</strong> <div class="note-content-value markdown-rendered"><p>None</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Code Of Conduct Acknowledgement:</strong> <span class="note-content-value">Yes</span></div><div><strong class="note-content-field disable-tex-rendering">Responsible Reviewing Acknowledgement:</strong> <span class="note-content-value">Yes</span></div></div></div><div class="note-replies"><div class="note  depth-even" data-id="zDuFGGajxz"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Rebuttal by Authors</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=zDuFGGajxz"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note zDuFGGajxz"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Rebuttal</span><span class="signatures">by <span>Authors (<span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to Conference, Authors"></span><a href="https://openreview.net/profile?id=~Huan_Huo1" target="_blank" rel="noreferrer">Huan Huo</a>, <a href="https://openreview.net/profile?id=~Yiran_Xiang1" target="_blank" rel="noreferrer">Yiran Xiang</a>, <a href="https://openreview.net/profile?id=~Guodong_Long1" target="_blank" rel="noreferrer">Guodong Long</a>, <a href="https://openreview.net/profile?id=~Xiufeng_Liu3" target="_blank" rel="noreferrer">Xiufeng Liu</a>, <a href="https://openreview.net/group/info?id=NeurIPS.cc/2025/Conference/Submission15547/Authors" target="_blank" rel="noreferrer">+3 more</a>)</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>30 Jul 2025, 17:15 (modified: 31 Jul 2025, 21:55)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=zDuFGGajxz">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Rebuttal:</strong> <div class="note-content-value markdown-rendered"><p>We sincerely thank Reviewer r41q for their time, thorough review, and constructive feedback on our submission. </p>
<p><strong>R3-1</strong>: For the selective distillation mechanism, although reverse KL is adopted to preserve client-specific modes, it is unclear why and how Q with low probability can indicate the personal modes of P. </p>
<p><strong>Re</strong>: We thank the reviewer for this excellent and detailed question, which points to a crucial aspect of our selective knowledge distillation mechanism that warrants further clarification. To clarify the behavior of reverse KL divergence <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="61" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-texatom size="s" texclass="ORD"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi></mjx-texatom></mjx-script></mjx-msub><mjx-mo class="mjx-n"><mjx-c class="mjx-c28"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mo class="mjx-n"><mjx-c class="mjx-c7C"></mjx-c></mjx-mo><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n"><mjx-c class="mjx-c29"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>D</mi><mrow data-mjx-texclass="ORD"><mi>K</mi><mi>L</mi></mrow></msub><mo stretchy="false">(</mo><mi>Q</mi><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mo data-mjx-texclass="ORD" stretchy="false">|</mo><mi>P</mi><mo stretchy="false">)</mo></math></mjx-assistive-mml></mjx-container> (Equation 14, lines 208-212), where <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="62" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> represents the global logits (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="63" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.247em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.29em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container>) and <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="64" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> represents the client's local logits (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="65" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.292em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.18em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mi>c</mi><mi>i</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container>): minimizing this divergence primarily encourages <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="66" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> to have high probability in regions where <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="67" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> also has high probability. The key for preserving personalization, however, lies in its asymmetric property: if <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="68" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container> has <strong>low probability</strong> (or near-zero probability), <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="69" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> is <strong>not penalized</strong> for having high probability in those regions. This means that if a client's local model (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="70" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container>) has developed unique, "personal modes" or specialized knowledge that is not strongly represented in the global model (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="71" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container>), it is allowed to <strong>retain these unique features</strong> without incurring a significant penalty during distillation. Thus, it's not that <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="72" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container>'s low probability <strong>indicates</strong> <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="73" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container>'s personal modes, but rather that <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="74" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container>'s low probability <strong>provides the freedom for <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="75" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container> to maintain its own strong modes</strong> that might diverge from the global consensus, thereby preserving client-specific knowledge. We propose integrating these detailed clarifications into Section 3.5 ("Selective Knowledge Distillation") to ensure the explanation of reverse KL's role in preserving personalization is precise and easy to grasp.</p>
<p><strong>R3-2</strong>: Intuitively, it should be determined by the clients which parts are most relevant to them. </p>
<p><strong>Re</strong>: We understand the intuition. However, in FedFuse, the selective determination of "which parts are most relevant" is achieved through a multi-stage process where the client-side mechanism complements the server-side MoE. Since the MoE's expert routing (Section 3.4) already performs a sophisticated, adaptive server-side relevance selection by dynamically weighting and routing client logits to specialized experts, the client's explicit determination on which parts of the global logits are most relevant becomes implicitly handled. The client-side selective knowledge distillation (Section 3.5) then takes this globally fused knowledge (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="76" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D444 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>Q</mi></math></mjx-assistive-mml></mjx-container>) and selectively integrates it by allowing local modes (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="77" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D443 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>P</mi></math></mjx-assistive-mml></mjx-container>) to persist through the properties of reverse KL divergence, as explained in R3-1. This ensures that only relevant global knowledge is assimilated without disrupting personalized features.</p>
<p><strong>R3-3</strong>: Besides, it is not clarified whether all logits in <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="78" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.247em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.29em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container> are equally distilled or filtered. Could adopting more aggressive filtering strategy further preserve personalization? The authors should provide detailed explanation and convincing experimental validation.</p>
<p><strong>Re</strong>: To clarify, in each training round, there exists exactly one set of server-side logits <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="79" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.247em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.29em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container> (Equation 13, lines 201-202), which is generated from the global MoE model on the public dataset <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="80" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container>. This entire set of global logits is distributed to all participating clients for distillation. The "selectivity" of our distillation does not come from explicitly filtering certain logits from <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="81" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-texatom texclass="ORD"><mjx-mover><mjx-over style="padding-bottom: 0.105em; padding-left: 0.272em; margin-bottom: -0.215em;"><mjx-mo class="mjx-n" style="width: 0px; margin-left: -0.25em;"><mjx-c class="mjx-c7E"></mjx-c></mjx-mo></mjx-over><mjx-base><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D450 TEX-I"></mjx-c></mjx-mi></mjx-base></mjx-mover></mjx-texatom><mjx-script style="vertical-align: -0.247em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D461 TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.29em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D454 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mrow data-mjx-texclass="ORD"><mover><mi>c</mi><mo stretchy="false">~</mo></mover></mrow><mi>g</mi><mi>t</mi></msubsup></math></mjx-assistive-mml></mjx-container>. Instead, it stems directly from the <strong>nature of the reverse KL divergence itself</strong> (Equation 14, Section 3.5), which implicitly acts as a "selective integration" mechanism. It selectively integrates global knowledge by encouraging the client to align with the global model's high-probability regions, while inherently "filtering" (or more accurately, allowing persistence of) local modes that are not strongly reflected in the global knowledge.
Regarding adopting a more aggressive explicit filtering strategy (e.g., thresholding based on confidence or divergence for specific data points), this is an interesting direction. However, adopting such strategies would indeed risk impairing performance in our current framework. Our ablation study (Table 3), where "w/o Personalized Update Lp" shows a significant performance drop (7.2 percentage points), empirically validates the necessity and effectiveness of our <code>Lp</code> term, which uses reverse KL. Without MoE's dynamic routing, excessive explicit filtering would discard valuable cross-client knowledge, leading to client over-isolation and ultimately failing to achieve effective knowledge sharing. Our current approach with reverse KL provides a continuous and adaptive form of "filtering" that has proven highly effective. We will add a sentence to the Conclusion (Section 5) to acknowledge the potential of ``exploring more aggressive explicit filtering strategies" as a promising avenue for future research.</p>
</div></div></div></div></div><div class="note  depth-even" data-id="eIe8vkV7fg"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Rebuttal by Authors</h5></div><div class="heading"><h4><span>Official Comment by Reviewer r41q</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=eIe8vkV7fg"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note eIe8vkV7fg"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(187, 187, 255); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Comment</span><span class="signatures">by <span>Reviewer r41q</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>05 Aug 2025, 10:00</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Comment:</strong> <div class="note-content-value markdown-rendered"><p>Thanks for the authors' rebuttal, which addresses some of my concerns, but the discussion on adopting more aggressive filtering strategy remains unconvincing. Therefore, I will keep my score.</p>
</div></div></div></div></div><div class="note  depth-even" data-id="ssueuSWxGa"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="parent-title"><h5><span class="glyphicon glyphicon-share-alt " aria-hidden="true"></span> Replying to Rebuttal by Authors</h5></div><div class="heading"><h4><span>Mandatory Acknowledgement by Reviewer r41q</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=ssueuSWxGa"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note ssueuSWxGa"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Mandatory Acknowledgement</span><span class="signatures">by <span>Reviewer r41q</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>05 Aug 2025, 10:01</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Mandatory Acknowledgement:</strong> <span class="note-content-value">I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. <a rel="noopener noreferrer" href="https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/" target="_blank">https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/</a></span></div></div></div></div></div></div><div class="note  depth-odd" data-id="exT1LYN36I"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Official Review of Submission15547 by Reviewer Z5Bu</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=exT1LYN36I"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note exT1LYN36I"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 187, 187); color: rgb(44, 58, 74);" data-original-title="Reply type">Official Review</span><span class="signatures">by <span>Reviewer Z5Bu</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>30 Jun 2025, 08:47 (modified: 24 Jul 2025, 16:36)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewer_Z5Bu"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer Z5Bu</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=exT1LYN36I">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Summary:</strong> <div class="note-content-value markdown-rendered"><p>This paper addresses heterogeneous federated learning where clients have diverse model architectures and non-IID data distributions. The authors propose FedFuse, which combines two key mechanisms: (1) a server-side expert-guided fusion using MoE to adaptively aggregate client knowledge representations (logits) from a public dataset, and (2) a selective knowledge distillation strategy using reverse KL divergence to integrate global knowledge while preserving client personalization. The framework operates by having clients generate logits on a public dataset, which are then processed by a global MoE model that learns to route and weight contributions from different clients to specialized experts. The refined global logits are distributed back to clients for personalized updates. The authors provide theoretical convergence analysis for both convex and non-convex cases and evaluate on three computer vision datasets with up to 500 clients.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Strengths And Weaknesses:</strong> <div class="note-content-value markdown-rendered"><p>Strengths: </p>
<p>The paper addresses a relevant challenge in federated learning - handling both statistical and architectural heterogeneity while preserving personalized performance. The integration of MoE for server-side aggregation with selective knowledge distillation presents an interesting approach to adaptive knowledge fusion in FL. The paper compares against multiple relevant baselines in both homogeneous and heterogeneous settings. The experimental evaluation demonstrates scalability with up to 500 clients and shows consistent improvements over baseline methods across different heterogeneity levels. The authors also provide ablation studies validating the necessity of both core components.</p>
<p>Weaknesses:</p>
<p>The paper has several weaknesses.</p>
<p>First, the dataset handling lacks clarity. The authors mention that each client has private data D_i^r and use a public dataset D^b for knowledge fusion, but they do not explain how CIFAR-100, TinyImageNet, and Flowers102 are split between private and public portions. It remains unclear whether the public dataset is a separate subset or part of the same data. The size and composition of the public dataset are not specified. The paper also does not clarify which data splits are used for validation and testing.</p>
<p>Second, the experimental design has limitations. The authors use only one epoch for both local and server training without explaining this choice or testing other epoch sizes. Key hyperparameters like temperature τ=2.0 are kept constant across all experiments without ablation studies. The paper does not mention whether baseline methods use the same training settings, which makes comparison difficult.</p>
<p>Third, the evaluation is limited to image classification tasks. Testing only on CIFAR-100, TinyImageNet, and Flowers102 datasets restricts the claims about heterogeneous federated learning. Including experiments on NLP or other domains would strengthen the evaluation. While the authors use Dirichlet distribution for non-IID data, they do not show the resulting class distributions across clients or provide quantitative metrics for heterogeneity.</p>
<p>Fourth, the benefit of using MoE appears marginal compared to its computational cost. The ablation study shows that MoE improves accuracy by only 0.56%, which raises questions about whether the added complexity is worthwhile. The choice of MoE parameters (E=100 experts for K=100 clients, top-k=40) lacks justification. The paper does not compare MoE with other adaptive aggregation methods. 
Additionally, the authors do not validate their theoretical assumptions empirically or analyze how MoE affects convergence. The paper also lacks analysis of how MoE scales in terms of memory and computation as client numbers increase.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Quality:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Clarity:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Significance:</strong> <span class="note-content-value">2: fair</span></div><div><strong class="note-content-field disable-tex-rendering">Originality:</strong> <span class="note-content-value">3: good</span></div><div><strong class="note-content-field disable-tex-rendering">Questions:</strong> <div class="note-content-value markdown-rendered"><p>How exactly are the datasets split between private client data and the public dataset? What percentage of data is public vs. private?</p>
<p>Why was only 1 epoch chosen for training? Have you experimented with different epoch sizes and their impact on performance?</p>
<p>Given the marginal improvement (0.56%) from MoE, what is the actual computational overhead in terms of memory and FLOPs?</p>
<p>How were the MoE parameters (number of experts, top-k) determined? What happens with different configurations?</p>
<p>How does the method handle non-vision tasks? Have you tested on any NLP or tabular datasets?</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Limitations:</strong> <div class="note-content-value markdown-rendered"><p>The authors addressed the limitations.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Rating:</strong> <span class="note-content-value">4: Borderline accept: Technically solid paper where reasons to accept outweigh reasons to reject, e.g., limited evaluation. Please use sparingly.</span></div><div><strong class="note-content-field disable-tex-rendering">Confidence:</strong> <span class="note-content-value">3: You are fairly confident in your assessment. It is possible that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work. Math/other details were not carefully checked.</span></div><div><strong class="note-content-field disable-tex-rendering">Ethical Concerns:</strong> <span class="note-content-value">NO or VERY MINOR ethics concerns only</span></div><div><strong class="note-content-field disable-tex-rendering">Paper Formatting Concerns:</strong> <div class="note-content-value markdown-rendered"><p>There is no major formatting concern.</p>
</div></div><div><strong class="note-content-field disable-tex-rendering">Code Of Conduct Acknowledgement:</strong> <span class="note-content-value">Yes</span></div><div><strong class="note-content-field disable-tex-rendering">Responsible Reviewing Acknowledgement:</strong> <span class="note-content-value">Yes</span></div></div></div><div class="note-replies"><div class="note  depth-even" data-id="LJLcyLP8EP"><div class="btn-group-vertical btn-group-xs collapse-controls-v" role="group" aria-label="Collapse controls"><button type="button" class="btn btn-default ">−</button><button type="button" class="btn btn-default middle ">＝</button><button type="button" class="btn btn-default active">≡</button></div><div class="heading"><h4><span>Rebuttal by Authors</span></h4><button type="button" class="btn btn-xs permalink-btn"><a href="https://openreview.net/forum?id=9ycNV8yhw8&amp;noteId=LJLcyLP8EP"><span class="glyphicon glyphicon-link " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Copy URL of note LJLcyLP8EP"></span></a></button></div><div class="subheading"><span class="invitation highlight" data-toggle="tooltip" data-placement="top" title="" style="background-color: rgb(255, 136, 204); color: rgb(44, 58, 74);" data-original-title="Reply type">Rebuttal</span><span class="signatures">by <span>Authors (<span class="glyphicon glyphicon-eye-open " data-toggle="tooltip" data-placement="top" title="" aria-hidden="true" data-original-title="Identities privately revealed to Conference, Authors"></span><a href="https://openreview.net/profile?id=~Huan_Huo1" target="_blank" rel="noreferrer">Huan Huo</a>, <a href="https://openreview.net/profile?id=~Yiran_Xiang1" target="_blank" rel="noreferrer">Yiran Xiang</a>, <a href="https://openreview.net/profile?id=~Guodong_Long1" target="_blank" rel="noreferrer">Guodong Long</a>, <a href="https://openreview.net/profile?id=~Xiufeng_Liu3" target="_blank" rel="noreferrer">Xiufeng Liu</a>, <a href="https://openreview.net/group/info?id=NeurIPS.cc/2025/Conference/Submission15547/Authors" target="_blank" rel="noreferrer">+3 more</a>)</span></span><span class="created-date" data-toggle="tooltip" data-placement="top" title="" data-original-title="Date created"><span class="glyphicon glyphicon-calendar " aria-hidden="true"></span>30 Jul 2025, 17:13 (modified: 31 Jul 2025, 21:55)</span><span class="readers" data-toggle="tooltip" data-placement="top" title="" data-original-title="Visible to &lt;br/&gt;NeurIPS.cc/2025/Conference/Program_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Reviewers/Submitted,&lt;br/&gt;NeurIPS.cc/2025/Conference/Submission15547/Authors"><span class="glyphicon glyphicon-eye-open " aria-hidden="true"></span>Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors</span><span class="revisions"><span class="glyphicon glyphicon-duplicate " aria-hidden="true"></span><a href="https://openreview.net/revisions?id=LJLcyLP8EP">Revisions</a></span></div><div class="note-content-container "><div class="note-content"><div><strong class="note-content-field disable-tex-rendering">Rebuttal:</strong> <div class="note-content-value markdown-rendered"><p>Thanks for your comments. </p>
<p><strong>R4-1</strong>: How exactly are the datasets split between private client data and the public dataset? </p>
<p><strong>Re</strong>:We appreciate the need for greater clarity on dataset specifics. We clarify the dataset handling as follows:</p>
<ol>
<li>Private vs. Public Data Split: Each client <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="82" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>i</mi></math></mjx-assistive-mml></mjx-container> possesses <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="83" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.292em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45F TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.18em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mi>D</mi><mi>i</mi><mi>r</mi></msubsup></math></mjx-assistive-mml></mjx-container>, which refers to their <strong>entire local private training dataset</strong>. The public dataset <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="84" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> is a <strong>small, separate, publicly available subset</strong> of the training portion of the respective dataset (CIFAR-100, TinyImageNet, Flowers102). It is crucial to note that <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="85" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> is <strong>not a part of <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="86" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.292em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45F TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.18em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mi>D</mi><mi>i</mi><mi>r</mi></msubsup></math></mjx-assistive-mml></mjx-container></strong>; clients retain full privacy over their <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="87" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.292em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45F TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.18em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mi>D</mi><mi>i</mi><mi>r</mi></msubsup></math></mjx-assistive-mml></mjx-container>.</li>
<li>Size and Composition of <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="88" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container>: For all experiments, we randomly sample a fixed subset of <strong>500 images</strong> (corresponding to a sample ratio of approximately 0.02 of the total training data for CIFAR-100) from the training set of the respective dataset to serve as <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="89" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container>. This size was chosen to be small enough to represent a practical public dataset, yet sufficient for knowledge representation and training the MoE gating network.</li>
<li>Validation and Testing Splits: For each dataset (CIFAR-100, TinyImageNet, Flowers102), we adhere to their <strong>standard training and testing splits</strong>. Clients train on their partitioned <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="90" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msubsup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.292em; margin-left: 0px;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D45F TEX-I"></mjx-c></mjx-mi><mjx-spacer style="margin-top: 0.18em;"></mjx-spacer><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D456 TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msubsup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msubsup><mi>D</mi><mi>i</mi><mi>r</mi></msubsup></math></mjx-assistive-mml></mjx-container> (derived from the training split), and accuracy is consistently reported on their <strong>local test sets</strong> (lines 240-241). The <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="91" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msup><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D437 TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: 0.363em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44F TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msup></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>D</mi><mi>b</mi></msup></math></mjx-assistive-mml></mjx-container> is always taken from the training portion of the respective dataset to avoid any data leakage from the test set.</li>
</ol>
<p>We propose integrating these clarifications into Section 4 ("Experiments") and Appendix E.2 ("Implementation Details").</p>
<p><strong>R4-2</strong>: Why was only 1 epoch chosen for training? Have you experimented with different epoch sizes and their impact on performance?   </p>
<p><strong>Re</strong>: We appreciate these important points regarding experimental rigor and transparency. We deliberately chose 'Ec=1' (local epochs) and 'Es=1' (server epochs) (Appendix E.2, lines 618-619) for two main reasons:</p>
<ol>
<li>Communication Efficiency Simulation: This choice simulates <strong>communication-efficient Federated Learning settings</strong>, where frequent communication rounds with minimal local computation per round are often preferred to minimize total training time and resource consumption, especially in large-scale scenarios. This ensures a fair comparison with baselines under a consistent communication budget across methods.</li>
<li>Mitigation of Overfitting in Extreme Non-IID Settings: We empirically validated this by testing configurations from epoch = 1 to 5, as shown in Table below. The results demonstrate that while epoch=2 yields a slightly higher accuracy, larger epoch values (3-5) for local training can indeed induce overfitting given the extremely heterogeneous Dirichlet-based data partitioning.</li>
</ol>
<p>Thus, our chosen 1-epoch setting represents a robust trade-off, prioritizing communication efficiency and preventing overfitting in highly heterogeneous environments. We confirm that baselines were implemented using their original recommended settings or tuned for fair comparison (Appendix E.2, lines 630-631), ensuring they were not disadvantaged. While a dedicated ablation study on the temperature <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="92" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c32"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi><mo>=</mo><mn>2.0</mn></math></mjx-assistive-mml></mjx-container> (lines 169, 622) could provide further insights, it was considered beyond the primary scope given other extensive evaluations. We will add the justification for the 1-epoch training choice in Appendix E.2 and suggest "more extensive ablation studies on hyperparameters like temperature <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="93" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> for diverse heterogeneity levels" as a valuable direction for future work in the Conclusion (Section 5).</p>
<p><strong>Table 1: Training epochs comparison (CIFAR-100, Dir α=0.1, 10 clients)</strong></p>
<table>
<thead>
<tr>
<th>Epoch</th>
<th>Accuracy (%)</th>
<th>Overfitting</th>
</tr>
</thead>
<tbody><tr>
<td>1</td>
<td>42.22</td>
<td>No</td>
</tr>
<tr>
<td>2</td>
<td>42.48</td>
<td>No</td>
</tr>
<tr>
<td>3</td>
<td>41.92</td>
<td>Yes</td>
</tr>
<tr>
<td>4</td>
<td>40.85</td>
<td>Yes</td>
</tr>
<tr>
<td>5</td>
<td>39.69</td>
<td>Yes</td>
</tr>
</tbody></table>
<p><strong>Table 2: Performance Comparison (CIFAR-100, Dir α=0.1, 50 clients)</strong></p>
<table>
<thead>
<tr>
<th>Model</th>
<th>Client Accuracy Variance</th>
<th>Low-frequency Client Accuracy</th>
</tr>
</thead>
<tbody><tr>
<td><strong>FedFuse</strong></td>
<td>18.03 (↑40.36%)</td>
<td>29.49% (↑13.59%)</td>
</tr>
<tr>
<td>Baseline w/o MoE</td>
<td>30.23</td>
<td>25.96%</td>
</tr>
</tbody></table>
<p><strong>Table 3: Computational Costs (CIFAR-100, Dir α=0.1, 50 clients)</strong></p>
<table>
<thead>
<tr>
<th>Model</th>
<th>FLOPS</th>
<th>Memory</th>
</tr>
</thead>
<tbody><tr>
<td><strong>FedFuse</strong></td>
<td>100G</td>
<td>808.08MB</td>
</tr>
<tr>
<td>Baseline w/o MoE</td>
<td>99.1G</td>
<td>726.41MB</td>
</tr>
</tbody></table>
<p><strong>R4-4</strong>: How were the MoE parameters (number of experts, top-k) determined? What happens with different configurations?</p>
<p><strong>Re</strong>: The choice of MoE parameters, <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="94" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi></math></mjx-assistive-mml></mjx-container> (total experts) and <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="95" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> (top-<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="96" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> active experts) (Appendix E.2, lines 623-624), was determined through preliminary experiments to balance performance and efficiency.</p>
<ol>
<li>Number of Experts (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="97" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>E</mi></math></mjx-assistive-mml></mjx-container>): We set the total number of experts to match the number of clients (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="98" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43E TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>K</mi></math></mjx-assistive-mml></mjx-container>). This design offers the maximum personalization capacity—each client can, in theory, have a dedicated expert. This proportionality allows for sufficient specialization within the MoE framework to handle diverse client knowledge.</li>
<li>Top-<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="99" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> Parameter: To ensure computational and memory efficiency, our gating network adopts a top-<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="100" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi></math></mjx-assistive-mml></mjx-container> sparse routing strategy (lines 181-183), where only a subset of experts is activated for each client in each round. As illustrated in Figure 5 (Appendix), activating around 40% of the total experts (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="101" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D458 TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c30"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c34"></mjx-c></mjx-mn><mjx-mo class="mjx-n" space="3"><mjx-c class="mjx-cD7"></mjx-c></mjx-mo><mjx-mi class="mjx-i" space="3"><mjx-c class="mjx-c1D438 TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>k</mi><mo>=</mo><mn>0.4</mn><mo>×</mo><mi>E</mi></math></mjx-assistive-mml></mjx-container>) achieves the best trade-off between performance and efficiency. This value was chosen to ensure sufficient knowledge routing while maintaining computational sparsity.</li>
</ol>
<p>Although expert redundancy may exist (e.g., if multiple clients benefit from similar experts), FedFuse effectively limits GPU memory overhead as shown in Table(Computational Costs), ensuring the framework remains computationally efficient. The theoretical analysis (Section 3.6, Appendix C.2) provides convergence guarantees for the overall FedFuse framework, which implicitly accounts for MoE's role in the aggregation loss (<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="102" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-msub><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D43F TEX-I"></mjx-c></mjx-mi><mjx-script style="vertical-align: -0.15em;"><mjx-mi class="mjx-i" size="s"><mjx-c class="mjx-c1D44E TEX-I"></mjx-c></mjx-mi></mjx-script></mjx-msub></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><msub><mi>L</mi><mi>a</mi></msub></math></mjx-assistive-mml></mjx-container>). Empirical validation of MoE's effect on convergence is shown in Figure 3, where FedFuse (with MoE) consistently converges faster and to higher accuracy. These settings ensure a balance between expressivity and computational tractability.</p>
<p><strong>R4-5</strong>: Key hyperparameters like temperature <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="103" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container>=2.0 are kept constant across all experiments without ablation studies. The paper does not mention whether baseline methods use the same training settings, which makes comparison difficult.</p>
<p><strong>Re</strong>: We clarify that all baseline methods and our proposed approach are trained under the same experimental setup, including optimizer, learning rate, batch size, number of local steps, and temperature <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="104" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi></math></mjx-assistive-mml></mjx-container> where applicable. Specifically, for distillation-based methods, we consistently set <mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="105" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mi class="mjx-i"><mjx-c class="mjx-c1D70F TEX-I"></mjx-c></mjx-mi><mjx-mo class="mjx-n" space="4"><mjx-c class="mjx-c3D"></mjx-c></mjx-mo><mjx-mn class="mjx-n" space="4"><mjx-c class="mjx-c32"></mjx-c><mjx-c class="mjx-c2E"></mjx-c><mjx-c class="mjx-c30"></mjx-c></mjx-mn></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mi>τ</mi><mo>=</mo><mn>2.0</mn></math></mjx-assistive-mml></mjx-container> following common practice, unless a method proposes its own tuning strategy.</p>
<p><strong>R4-6</strong>: How does the method handle non-vision tasks? Have you tested on any NLP or tabular datasets?</p>
<p><strong>Re</strong>: This is a very insightful observation and a valuable direction for future exploration. We agree that demonstrating compatibility with transformer-based models would be valuable. We have expanded our experiments to include transformer-based models on an NLP dataset (AGNews) to demonstrate the generalizability of FedFuse. The complete experimental results and model configurations are systematically presented in Table below.
FedFuse is fundamentally <strong>architecture-agnostic</strong> due to its dual design, which primarily operates on logits (lines 7, 43, 145-146):</p>
<ol>
<li>Logits-level operations—including MoE-based fusion and selective distillation—are decoupled from specific model architectures. The dynamic gating operates on generic feature representations (which can be derived from CNN activations or Transformer embeddings) and naturally adapts to different architectures.</li>
<li>Selective distillation depends solely on the output distribution (logits), rather than architecture-specific internal representations, ensuring compatibility across diverse model backbones, including transformer-based models.</li>
</ol>
<p><strong>Table 4: Results on AGNews (50 clients)</strong></p>
<table>
<thead>
<tr>
<th>Algo</th>
<th>Ours</th>
<th>FedKD</th>
<th>FedProto</th>
<th>FedMRL</th>
<th>FedTGP</th>
</tr>
</thead>
<tbody><tr>
<td>Acc(%)</td>
<td>96.11 (23.73%<mjx-container class="MathJax CtxtMenu_Attached_0" jax="CHTML" tabindex="0" ctxtmenu_counter="106" style="font-size: 121.3%; position: relative;"><mjx-math class="MJX-TEX" aria-hidden="true"><mjx-mo class="mjx-n"><mjx-c class="mjx-c2191"></mjx-c></mjx-mo></mjx-math><mjx-assistive-mml unselectable="on" display="inline"><math xmlns="http://www.w3.org/1998/Math/MathML"><mo stretchy="false">↑</mo></math></mjx-assistive-mml></mjx-container>)</td>
<td>94.80</td>
<td>77.68</td>
<td>94.82</td>
<td>94.76</td>
</tr>
</tbody></table>
<p><strong>R4-7</strong>: While the authors use Dirichlet distribution for non-IID data, they do not show the resulting class distributions across clients or provide quantitative metrics for heterogeneity.  </p>
<p><strong>Re</strong>: To quantify the heterogeneity, we have computed the average class distribution entropy and pairwise Jensen–Shannon divergence (JSD) across clients. These metrics provide a concrete measure of how imbalanced and dissimilar the client datasets are.</p>
<h3>Class Distributions (10 clients)</h3>
<table>
<thead>
<tr>
<th>Dirichlet (α)</th>
<th>0.05</th>
<th>0.1</th>
<th>0.5</th>
</tr>
</thead>
<tbody><tr>
<td><strong>Avg Entropy</strong></td>
<td>2.8225</td>
<td>3.1293</td>
<td>3.9345</td>
</tr>
<tr>
<td><strong>Avg JSD</strong></td>
<td>0.6162</td>
<td>0.5601</td>
<td>0.3227</td>
</tr>
</tbody></table>
<p><em>Table: Class Distributions (10 clients).</em></p>
<p><strong>R4-9</strong>: The paper also lacks analysis of how MoE scales in terms of memory and computation as client numbers increase.</p>
<p><strong>Re</strong>: We have conducted additional experiments measuring the server-side GPU memory consumption under varying numbers of clients (e.g., 10, 20, 50, 100, 200, 500), while keeping the total number of experts fixed. Memory consumption remains well within feasible limits, even with increasing model size or expert count.</p>
<h3>Memory Usage vs Client Numbers (CIFAR-100)</h3>
<table>
<thead>
<tr>
<th>Client Numbers</th>
<th>10</th>
<th>20</th>
<th>50</th>
<th>100</th>
<th>200</th>
<th>500</th>
</tr>
</thead>
<tbody><tr>
<td><strong>Memory (MB)</strong></td>
<td>680.28</td>
<td>707.23</td>
<td>808.08</td>
<td>864.95</td>
<td>902.43</td>
<td>1,958.63</td>
</tr>
</tbody></table>
</div></div></div></div></div></div></div></div></div></div></div></div></main></div></div><footer class="sitemap"><div class="container"><div class="row hidden-xs"><div class="col-sm-4"><ul class="list-unstyled"><li><a href="https://openreview.net/about">About OpenReview</a></li><li><a href="https://openreview.net/group?id=OpenReview.net/Support">Hosting a Venue</a></li><li><a href="https://openreview.net/venues">All Venues</a></li></ul></div><div class="col-sm-4"><ul class="list-unstyled"><li><a href="https://openreview.net/contact">Contact</a></li><li><a href="https://openreview.net/sponsors">Sponsors</a></li><li><a class="join-the-team" href="https://codeforscience.org/jobs?job=OpenReview-Developer" target="_blank" rel="noopener noreferrer"><strong>Join the Team</strong></a></li></ul></div><div class="col-sm-4"><ul class="list-unstyled"><li><a href="https://docs.openreview.net/getting-started/frequently-asked-questions">Frequently Asked Questions</a></li><li><a href="https://openreview.net/legal/terms">Terms of Use</a></li><li><a href="https://openreview.net/legal/privacy">Privacy Policy</a></li></ul></div></div><div class="row visible-xs-block"><div class="col-xs-6"><ul class="list-unstyled"><li><a href="https://openreview.net/about">About OpenReview</a></li><li><a href="https://openreview.net/group?id=OpenReview.net/Support">Hosting a Venue</a></li><li><a href="https://openreview.net/venues">All Venues</a></li><li><a href="https://openreview.net/sponsors">Sponsors</a></li><li><a class="join-the-team" href="https://codeforscience.org/jobs?job=OpenReview-Developer" target="_blank" rel="noopener noreferrer"><strong>Join the Team</strong></a></li></ul></div><div class="col-xs-6"><ul class="list-unstyled"><li><a href="https://docs.openreview.net/getting-started/frequently-asked-questions">Frequently Asked Questions</a></li><li><a href="https://openreview.net/contact">Contact</a></li><li><a href="https://openreview.net/legal/terms">Terms of Use</a></li><li><a href="https://openreview.net/legal/privacy">Privacy Policy</a></li></ul></div></div></div></footer><footer class="sponsor"><div class="container"><div class="row"><div class="col-sm-10 col-sm-offset-1"><p class="text-center"><a href="https://openreview.net/about" target="_blank">OpenReview</a> <!-- -->is a long-term project to advance science through improved peer review with legal nonprofit status. We gratefully acknowledge the support of the<!-- --> <a href="https://openreview.net/sponsors" target="_blank">OpenReview Sponsors</a>. © <!-- -->2025<!-- --> OpenReview</p></div></div></div></footer></div><script src="./FedFuse_files/webpack-9660e8bc1b33b385.js.download" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[64818,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563e166e8f5.js\"],\"default\"]\n3:I[6874,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6846\",\"static/chunks/6846-1cd184c18c60fea8.js\",\"1592\",\"static/chunks/1592-72921a7113ac822e.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"9032\",\"static/chunks/9032-8fb8561574ebe0ce.js\",\"9251\",\"static/chunks/9251-4963718882dacbdc.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"2882\",\"static/chunks/2882-66622e31b0f52a02.js\",\"1399\",\"static/chunks/1399-071a12ebad7508dd.js\",\"4757\",\"static/chunks/4757-73521c726a0e013c.js\",\"4745\",\"static/chunks/4745-960031cc83ae4c6c.js\",\"3474\",\"static/chunks/3474-fc09b016da95fe81.js\",\"5262\",\"static/chunks/5262-e24c2f70e61a06c6.js\",\"5300\",\"static/chunks/app/forum/page-ae19d00834327398.js\"],\"\"]\n4:I[41316,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4"])</script><script>self.__next_f.push([1,"d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563e166e8f5.js\"],\"default\"]\n6:I[46967,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563e166e8f5.js\"],\"default\"]\n7:I[87555,[],\"\"]\n8:I[31702,[\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"4757\",\"static/chunks/4757-73521c726a0e013c.js\",\"8039\",\"static/chunks/app/error-b150b0fce1b36d7b.js\"],\"default\"]\n9:I[31295,[],\"\"]\na:I[64757,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6846\",\"static/chunks/6846-1cd184c18c60fea8.js\",\"1592\",\"static/chunks/1592-72921a7113ac822e.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"9032\",\"static/chunks/9032-8fb8561574ebe0ce.js\",\"9251\",\"static/chunks/9251-4963718882dacbdc.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"2882\",\"static/chunks/2882-66622e31b0f52a02.js\",\"1399\",\"static/chunks/1399-071a12ebad7508dd.js\",\"4757\",\"static/chunks/4757-73521c726a0e013c.js\",\"4745\",\"static/chunks/4745-960031cc83ae4c6c.js\",\"3474\",\"static/chunks/3474-fc09b016da95fe81.js\",\"5262\",\"static/chunks/5262-e24c2f70e61a06c6.js\",\"5300\",\"static/chunks/app/forum/page-ae19d00834327398.js\"],\"default\"]\nb:I[6924"])</script><script>self.__next_f.push([1,"3,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563e166e8f5.js\"],\"\"]\nd:I[59665,[],\"OutletBoundary\"]\n10:I[59665,[],\"ViewportBoundary\"]\n12:I[59665,[],\"MetadataBoundary\"]\n14:I[89340,[\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"4219\",\"static/chunks/app/global-error-437204fce1f23329.js\"],\"default\"]\n17:I[37081,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563e166e8f5.js\"],\"default\"]\n18:\"$Sreact.suspense\"\n19:I[32323,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"3740\",\"static/chunks/7ce798d6-3eb8122476a3f2e5.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"8780\",\"static/chunks/8780-219cd1efe9e9c581.js\",\"5226\",\"static/chunks/5226-3f9b6d1f505acbb7.js\",\"5930\",\"static/chunks/5930-ff1d66cb0fff6ec4.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"7177\",\"static/chunks/app/layout-96cbf563"])</script><script>self.__next_f.push([1,"e166e8f5.js\"],\"default\"]\n:HL[\"/_next/static/media/08f4947ad4536ee1-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/c4250770ab8708b6-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/623ec4d945fb0950.css\",\"style\"]\n:HL[\"/_next/static/css/7efbc204b5b07ee6.css\",\"style\"]\n:HL[\"/_next/static/css/8e7bf7099e0c2325.css\",\"style\"]\n:HL[\"/_next/static/css/7e1ff74241679440.css\",\"style\"]\n:HL[\"/_next/static/css/788c496c60915883.css\",\"style\"]\n:HL[\"/_next/static/css/9dffa5889d3de130.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"v1.14.11\",\"p\":\"\",\"c\":[\"\",\"forum?id=9ycNV8yhw8\u0026referrer=%5BAuthor%20Console%5D(%2Fgroup%3Fid%3DNeurIPS.cc%2F2025%2FConference%2FAuthors%23your-submissions)\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"forum\",{\"children\":[\"__PAGE__?{\\\"id\\\":\\\"9ycNV8yhw8\\\",\\\"referrer\\\":\\\"[Author Console](/group?id=NeurIPS.cc/2025/Conference/Authors#your-submissions)\\\"}\",{}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/623ec4d945fb0950.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7efbc204b5b07ee6.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"2\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/8e7bf7099e0c2325.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"3\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/7e1ff74241679440.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"icon\",\"href\":\"/favicon.ico\"}],[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/manifest.json\"}]]}],[\"$\",\"$L2\",null,{\"children\":[\"$\",\"body\",null,{\"className\":\"__className_086c6e\",\"children\":[\"$\",\"div\",null,{\"id\":\"__next\",\"children\":[[\"$\",\"nav\",null,{\"className\":\"navbar navbar-inverse\",\"role\":\"navigation\",\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[[\"$\",\"div\",null,{\"className\":\"navbar-header\",\"children\":[[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"navbar-toggle collapsed\",\"data-toggle\":\"collapse\",\"data-target\":\"#navbar\",\"aria-expanded\":\"false\",\"aria-controls\":\"navbar\",\"children\":[[\"$\",\"span\",null,{\"className\":\"sr-only\",\"children\":\"Toggle navigation\"}],[\"$\",\"span\",null,{\"className\":\"icon-bar\"}],[\"$\",\"span\",null,{\"className\":\"icon-bar\"}],[\"$\",\"span\",null,{\"className\":\"icon-bar\"}]]}],[\"$\",\"$L3\",null,{\"href\":\"/\",\"className\":\"navbar-brand home push-link\",\"children\":[[\"$\",\"strong\",null,{\"children\":\"OpenReview\"}],\".net\"]}]]}],[\"$\",\"div\",null,{\"id\":\"navbar\",\"className\":\"navbar-collapse collapse\",\"children\":[[\"$\",\"$L4\",null,{}],\"$L5\"]}]]}]}],[\"$\",\"div\",null,{\"id\":\"flash-message-container\",\"className\":\"alert alert-danger fixed-overlay\",\"role\":\"alert\",\"style\":{\"display\":\"none\"},\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"row\",\"children\":[\"$\",\"div\",null,{\"className\":\"col-xs-12\",\"children\":[\"$\",\"div\",null,{\"className\":\"alert-content\",\"children\":[\"$\",\"button\",null,{\"type\":\"button\",\"className\":\"close\",\"aria-label\":\"Close\",\"children\":[\"$\",\"span\",null,{\"aria-hidden\":\"true\",\"children\":\"×\"}]}]}]}]}]}]}],[\"$\",\"$L6\",null,{}],[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$8\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$La\",null,{\"statusCode\":404,\"message\":\"Please check that the URL is spelled correctly and try again.\"}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}]}]}],[[\"$\",\"$Lb\",null,{\"src\":\"https://www.googletagmanager.com/gtag/js?id=G-GTB25PBMVL\"}],[\"$\",\"$Lb\",null,{\"id\":\"ga-script\",\"dangerouslySetInnerHTML\":{\"__html\":\"window.dataLayer = window.dataLayer || [];\\nfunction gtag() { dataLayer.push(arguments); }\\ngtag('js', new Date());\\ngtag('config', 'G-GTB25PBMVL', {\\npage_location: location.origin + location.pathname + location.search,\\n});\"}}]]]}]]}],{\"children\":[\"forum\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L9\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$Lc\",\"$undefined\",[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/788c496c60915883.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}],[\"$\",\"link\",\"1\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/9dffa5889d3de130.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"$Ld\",null,{\"children\":[\"$Le\",\"$Lf\",null]}]]}],{},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"CkEfXK09vSGFtGlp0KhWU\",{\"children\":[[\"$\",\"$L10\",null,{\"children\":\"$L11\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],[\"$\",\"$L12\",null,{\"children\":\"$L13\"}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$14\",[]],\"s\":false,\"S\":false}\n"])</script><script>self.__next_f.push([1,"5:[\"$\",\"ul\",null,{\"className\":\"nav navbar-nav navbar-right\",\"children\":[[\"$\",\"li\",null,{\"className\":\"hidden-sm\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/notifications\",\"prefetch\":false,\"children\":[\"Notifications\",\"$L15\"]}]}],[\"$\",\"li\",null,{\"className\":\"hidden-sm\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/activity\",\"prefetch\":false,\"children\":\"Activity\"}]}],[\"$\",\"li\",null,{\"className\":\"hidden-sm\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/tasks\",\"prefetch\":false,\"children\":\"Tasks\"}]}],[\"$\",\"li\",null,{\"id\":\"user-menu\",\"className\":\"dropdown\",\"children\":[[\"$\",\"a\",null,{\"className\":\"dropdown-toggle\",\"data-toggle\":\"dropdown\",\"role\":\"button\",\"aria-haspopup\":\"true\",\"aria-expanded\":\"false\",\"children\":[[\"$\",\"span\",null,{\"children\":[\"Xiufeng Liu\",\"$undefined\"]}],\" \",[\"$\",\"span\",null,{\"className\":\"caret\"}]]}],[\"$\",\"ul\",null,{\"className\":\"dropdown-menu\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/profile\",\"children\":\"Profile\"}]}],[\"$\",\"li\",null,{\"className\":\"visible-sm-block\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/notifications\",\"prefetch\":false,\"children\":[\"Notifications\",\"$L16\"]}]}],[\"$\",\"li\",null,{\"className\":\"visible-sm-block\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/activity\",\"prefetch\":false,\"children\":\"Activity\"}]}],[\"$\",\"li\",null,{\"className\":\"visible-sm-block\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/tasks\",\"prefetch\":false,\"children\":\"Tasks\"}]}],[\"$\",\"li\",null,{\"role\":\"separator\",\"className\":\"divider hidden-xs\"}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L17\",null,{}]}]]}]]}]]}]\n15:[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[\"$\",\"$L19\",null,{\"notificationCountP\":\"$@1a\"}]}]\n16:[\"$\",\"$18\",null,{\"fallback\":null,\"children\":[\"$\",\"$L19\",null,{\"notificationCountP\":\"$@1b\"}]}]\n11:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\ne:null\n"])</script><script>self.__next_f.push([1,"1c:I[39677,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6846\",\"static/chunks/6846-1cd184c18c60fea8.js\",\"1592\",\"static/chunks/1592-72921a7113ac822e.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"9032\",\"static/chunks/9032-8fb8561574ebe0ce.js\",\"9251\",\"static/chunks/9251-4963718882dacbdc.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"2882\",\"static/chunks/2882-66622e31b0f52a02.js\",\"1399\",\"static/chunks/1399-071a12ebad7508dd.js\",\"4757\",\"static/chunks/4757-73521c726a0e013c.js\",\"4745\",\"static/chunks/4745-960031cc83ae4c6c.js\",\"3474\",\"static/chunks/3474-fc09b016da95fe81.js\",\"5262\",\"static/chunks/5262-e24c2f70e61a06c6.js\",\"5300\",\"static/chunks/app/forum/page-ae19d00834327398.js\"],\"default\"]\n1d:I[73775,[\"4935\",\"static/chunks/e37a0b60-86dcf540460bd9a6.js\",\"6874\",\"static/chunks/6874-8f3d6c72a87c2225.js\",\"3697\",\"static/chunks/3697-c0092b2c69fd8d8c.js\",\"590\",\"static/chunks/590-a7095dbf68fad767.js\",\"4540\",\"static/chunks/4540-1380528b77b77034.js\",\"6846\",\"static/chunks/6846-1cd184c18c60fea8.js\",\"1592\",\"static/chunks/1592-72921a7113ac822e.js\",\"6325\",\"static/chunks/6325-93a1b42c84bba41c.js\",\"9032\",\"static/chunks/9032-8fb8561574ebe0ce.js\",\"9251\",\"static/chunks/9251-4963718882dacbdc.js\",\"9433\",\"static/chunks/9433-4d56475ee6e9848e.js\",\"2882\",\"static/chunks/2882-66622e31b0f52a02.js\",\"1399\",\"static/chunks/1399-071a12ebad7508dd.js\",\"4757\",\"static/chunks/4757-73521c726a0e013c.js\",\"4745\",\"static/chunks/4745-960031cc83ae4c6c.js\",\"3474\",\"static/chunks/3474-fc09b016da95fe81.js\",\"5262\",\"static/chunks/5262-e24c2f70e61a06c6.js\",\"5300\",\"static/chunks/app/forum/page-ae19d00834327398.js\"],\"default\"]\n1e:T59a,Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during ag"])</script><script>self.__next_f.push([1,"gregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency."])</script><script>self.__next_f.push([1,"c:[[\"$\",\"$L1c\",null,{\"banner\":[\"$\",\"div\",null,{\"id\":\"or-banner\",\"className\":\"banner\",\"style\":null,\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"row\",\"children\":[\"$\",\"div\",null,{\"className\":\"col-xs-12\",\"children\":[\"$\",\"$L3\",null,{\"href\":\"/group?id=NeurIPS.cc/2025/Conference/Authors#your-submissions\",\"title\":\"Back\",\"children\":[[\"$\",\"img\",null,{\"className\":\"icon\",\"src\":\"/images/arrow_left.svg\",\"alt\":\"back arrow\"}],\"Back to\",\" \",[\"$\",\"strong\",null,{\"children\":\"Author Console\"}]]}]}]}]}]}]}],\"$undefined\",[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"row\",\"children\":[\"$\",\"main\",null,{\"id\":\"content\",\"children\":[\"$\",\"div\",null,{\"className\":\"Forum_forum__wS8Fw\",\"children\":[\"$\",\"$L1d\",null,{\"forumNote\":{\"content\":{\"title\":{\"value\":\"FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning\"},\"authors\":{\"value\":[\"Haizhou Du\",\"Yiran Xiang\",\"Lixin Huang\",\"Xiufeng Liu\",\"Huan Huo\",\"Zonghan Wu\",\"Guodong Long\"],\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"authorids\":{\"value\":[\"~Haizhou_Du2\",\"~Yiran_Xiang1\",\"~Lixin_Huang3\",\"~Xiufeng_Liu3\",\"~Huan_Huo1\",\"~Zonghan_Wu1\",\"~Guodong_Long1\"],\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"keywords\":{\"value\":[\"Knowledge Distillation\",\"Personalized Federated Learning\",\"Expert-Guided Fusion\"]},\"abstract\":{\"value\":\"$1e\"},\"pdf\":{\"value\":\"/pdf/7447fe5beb2dc1f8b994282bb94fc21b979fa179.pdf\"},\"checklist_confirmation\":{\"value\":true,\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"reviewer_nomination\":{\"value\":\"<EMAIL>\",\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"responsible_reviewing\":{\"value\":true,\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"primary_area\":{\"value\":\"general_machine_learning\"},\"LLM_usage\":{\"value\":[\"not_used\"],\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"declaration\":{\"value\":true,\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"financial_support\":{\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"other_LLM_usage\":{\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]},\"venue\":{\"value\":\"NeurIPS 2025 Conference Submission\"},\"venueid\":{\"value\":\"NeurIPS.cc/2025/Conference/Submission\"},\"supplementary_material\":{\"value\":\"/attachment/0c03f412eaad3010cb1dd97e0ef83ca603433aca.zip\"},\"paperhash\":{\"value\":\"du|fedfuse_selective_knowledge_distillation_with_expertguided_fusion_for_heterogeneous_federated_learning\",\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"]}},\"id\":\"9ycNV8yhw8\",\"forum\":\"9ycNV8yhw8\",\"license\":\"CC BY 4.0\",\"signatures\":[\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"],\"readers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Senior_Area_Chairs\",\"NeurIPS.cc/2025/Conference/Submission15547/Area_Chairs\",\"NeurIPS.cc/2025/Conference/Submission15547/Reviewers\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"],\"writers\":[\"NeurIPS.cc/2025/Conference\",\"NeurIPS.cc/2025/Conference/Submission15547/Authors\"],\"number\":15547,\"invitations\":[\"NeurIPS.cc/2025/Conference/-/Submission\",\"NeurIPS.cc/2025/Conference/-/Post_Submission\",\"NeurIPS.cc/2025/Conference/Submission15547/-/Full_Submission\"],\"domain\":\"NeurIPS.cc/2025/Conference\",\"tcdate\":1746868586399,\"cdate\":1746868586399,\"tmdate\":1748445262778,\"mdate\":1748445262778,\"version\":2,\"details\":{\"writable\":true,\"presentation\":[{\"name\":\"title\",\"order\":1,\"type\":\"string\"},{\"name\":\"authors\",\"order\":3},{\"name\":\"authorids\",\"order\":4},{\"name\":\"keywords\",\"order\":4,\"type\":\"string[]\"},{\"name\":\"TLDR\",\"order\":5,\"type\":\"string\",\"fieldName\":\"TL;DR\"},{\"name\":\"abstract\",\"order\":6,\"type\":\"string\",\"input\":\"textarea\",\"markdown\":true},{\"name\":\"pdf\",\"order\":7,\"type\":\"file\"},{\"name\":\"checklist_confirmation\",\"order\":8,\"type\":\"boolean\",\"input\":\"checkbox\",\"value\":true,\"description\":\"I confirm that I have included a paper checklist in the paper PDF.\"},{\"name\":\"supplementary_material\",\"order\":9,\"type\":\"file\"},{\"name\":\"financial_support\",\"order\":10,\"type\":\"string\"},{\"name\":\"reviewer_nomination\",\"order\":11,\"type\":\"string\"},{\"name\":\"responsible_reviewing\",\"order\":12,\"type\":\"boolean\",\"input\":\"checkbox\",\"value\":true,\"description\":\"We acknowledge the responsible reviewing obligations as authors.\"},{\"name\":\"primary_area\",\"order\":13,\"type\":\"string\",\"input\":\"select\",\"value\":\"general_machine_learning\",\"description\":\"General machine learning (supervised, unsupervised, online, active, etc.)\"},{\"name\":\"LLM_usage\",\"order\":14,\"type\":\"string[]\",\"input\":\"checkbox\",\"value\":[\"not_used\"],\"description\":[\"Not used at all (you can then skip the rest)\"]},{\"name\":\"other_LLM_usage\",\"order\":15,\"type\":\"string\",\"input\":\"textarea\"},{\"name\":\"declaration\",\"order\":16,\"type\":\"boolean\",\"input\":\"checkbox\",\"value\":true,\"description\":\"I confirm that the above information is accurate.\"},{\"name\":\"venue\",\"hidden\":true},{\"name\":\"venueid\",\"hidden\":true},{\"name\":\"_bibtex\",\"type\":\"string\",\"input\":\"textarea\"}]},\"apiVersion\":2},\"selectedNoteId\":\"$undefined\",\"selectedInvitationId\":\"$undefined\",\"prefilledValues\":{},\"query\":{\"id\":\"9ycNV8yhw8\",\"referrer\":\"[Author Console](/group?id=NeurIPS.cc/2025/Conference/Authors#your-submissions)\"}}]}]}]}]}],[[\"$\",\"footer\",null,{\"className\":\"sitemap\",\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[[\"$\",\"div\",null,{\"className\":\"row hidden-xs\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-sm-4\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-unstyled\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/about\",\"children\":\"About OpenReview\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/group?id=OpenReview.net/Support\",\"children\":\"Hosting a Venue\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/venues\",\"children\":\"All Venues\"}]}]]}]}],[\"$\",\"div\",null,{\"className\":\"col-sm-4\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-unstyled\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/contact\",\"children\":\"Contact\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/sponsors\",\"children\":\"Sponsors\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"className\":\"join-the-team\",\"href\":\"https://codeforscience.org/jobs?job=OpenReview-Developer\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"children\":[\"$\",\"strong\",null,{\"children\":\"Join the Team\"}]}]}]]}]}],[\"$\",\"div\",null,{\"className\":\"col-sm-4\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-unstyled\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"https://docs.openreview.net/getting-started/frequently-asked-questions\",\"children\":\"Frequently Asked Questions\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/legal/terms\",\"children\":\"Terms of Use\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/legal/privacy\",\"children\":\"Privacy Policy\"}]}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"row visible-xs-block\",\"children\":[[\"$\",\"div\",null,{\"className\":\"col-xs-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-unstyled\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/about\",\"children\":\"About OpenReview\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/group?id=OpenReview.net/Support\",\"children\":\"Hosting a Venue\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/venues\",\"children\":\"All Venues\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/sponsors\",\"children\":\"Sponsors\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"className\":\"join-the-team\",\"href\":\"https://codeforscience.org/jobs?job=OpenReview-Developer\",\"target\":\"_blank\",\"rel\":\"noopener noreferrer\",\"children\":[\"$\",\"strong\",null,{\"children\":\"Join the Team\"}]}]}]]}]}],[\"$\",\"div\",null,{\"className\":\"col-xs-6\",\"children\":[\"$\",\"ul\",null,{\"className\":\"list-unstyled\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"a\",null,{\"href\":\"https://docs.openreview.net/getting-started/frequently-asked-questions\",\"children\":\"Frequently Asked Questions\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/contact\",\"children\":\"Contact\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/legal/terms\",\"children\":\"Terms of Use\"}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L3\",null,{\"href\":\"/legal/privacy\",\"children\":\"Privacy Policy\"}]}]]}]}]]}]]}]}],[\"$\",\"footer\",null,{\"className\":\"sponsor\",\"children\":[\"$\",\"div\",null,{\"className\":\"container\",\"children\":[\"$\",\"div\",null,{\"className\":\"row\",\"children\":[\"$\",\"div\",null,{\"className\":\"col-sm-10 col-sm-offset-1\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-center\",\"children\":[[\"$\",\"a\",null,{\"href\":\"/about\",\"target\":\"_blank\",\"children\":\"OpenReview\"}],\" \",\"is a long-term project to advance science through improved peer review with legal nonprofit status. We gratefully acknowledge the support of the\",\" \",[\"$\",\"a\",null,{\"href\":\"/sponsors\",\"target\":\"_blank\",\"children\":\"OpenReview Sponsors\"}],\". © \",2025,\" OpenReview\"]}]}]}]}]}]]]\n"])</script><script>self.__next_f.push([1,"f:null\n1f:T59a,Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during aggregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency.20:T59a,Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to the loss of personalized knowledge during aggregation and potentially exacerbating client model divergence due to globally-guided updates misaligned with local data or architectures. To tackle the challenges, we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion mechanism that uniquely facili"])</script><script>self.__next_f.push([1,"tates adaptive knowledge fusion by dynamically gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients, diverse heterogeneity settings, and ablation studies validating the necessity of both core components, demonstrate our superiority. FedFuse significantly outperforms state-of-the-art methods in test accuracy, particularly under high heterogeneity, while exhibiting competitive communication and computational efficiency.13:[[\"$\",\"title\",\"0\",{\"children\":\"FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning | OpenReview\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"$1f\"}],[\"$\",\"meta\",\"2\",{\"name\":\"citation_title\",\"content\":\"FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion for Heterogeneous Federated Learning\"}],[\"$\",\"meta\",\"3\",{\"name\":\"citation_author\",\"content\":\"Haizhou Du\"}],[\"$\",\"meta\",\"4\",{\"name\":\"citation_author\",\"content\":\"Yiran Xiang\"}],[\"$\",\"meta\",\"5\",{\"name\":\"citation_author\",\"content\":\"Lixin Huang\"}],[\"$\",\"meta\",\"6\",{\"name\":\"citation_author\",\"content\":\"Xiufeng Liu\"}],[\"$\",\"meta\",\"7\",{\"name\":\"citation_author\",\"content\":\"Huan Huo\"}],[\"$\",\"meta\",\"8\",{\"name\":\"citation_author\",\"content\":\"Zonghan Wu\"}],[\"$\",\"meta\",\"9\",{\"name\":\"citation_author\",\"content\":\"Guodong Long\"}],[\"$\",\"meta\",\"10\",{\"name\":\"citation_publication_date\",\"content\":\"2025/05/10\"}],[\"$\",\"meta\",\"11\",{\"name\":\"citation_pdf_url\",\"content\":\"https://openreview.net/pdf?id=9ycNV8yhw8\"}],[\"$\",\"meta\",\"12\",{\"name\":\"citation_abstract\",\"content\":\"$20\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:title\",\"content\":\"FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion...\""])</script><script>self.__next_f.push([1,"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:description\",\"content\":\"Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to...\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:type\",\"content\":\"article\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:card\",\"content\":\"summary\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion...\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:description\",\"content\":\"Heterogeneous Federated Learning enables collaborative training across devices with diverse architectures and non-IID data. However, it often struggles with effective knowledge fusion, leading to...\"}]]\n"])</script><script>self.__next_f.push([1,"1a:{\"count\":225}\n1b:{\"count\":225}\n"])</script><script>$RC=function(b,c,e){c=document.getElementById(c);c.parentNode.removeChild(c);var a=document.getElementById(b);if(a){b=a.previousSibling;if(e)b.data="$!",a.setAttribute("data-dgst",e);else{e=b.parentNode;a=b.nextSibling;var f=0;do{if(a&&8===a.nodeType){var d=a.data;if("/$"===d)if(0===f)break;else f--;else"$"!==d&&"$?"!==d&&"$!"!==d||f++}d=a.nextSibling;e.removeChild(a);a=d}while(a);for(;c.firstChild;)e.insertBefore(c.firstChild,a);b.data="$"}b._reactRetry&&b._reactRetry()}};$RC("B:0","S:0")</script><script>$RC("B:1","S:1")</script><script src="./FedFuse_files/tex-chtml-full.js.download" data-nscript="afterInteractive"></script><script src="./FedFuse_files/js" data-nscript="afterInteractive"></script><script id="ga-script" data-nscript="afterInteractive">window.dataLayer = window.dataLayer || [];
function gtag() { dataLayer.push(arguments); }
gtag('js', new Date());
gtag('config', 'G-GTB25PBMVL', {
page_location: location.origin + location.pathname + location.search,
});</script><next-route-announcer style="position: absolute;"><template shadowrootmode="open"><div aria-live="assertive" id="__next-route-announcer__" role="alert" style="position: absolute; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: rect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wrap: normal;"></div></template></next-route-announcer></body></html>