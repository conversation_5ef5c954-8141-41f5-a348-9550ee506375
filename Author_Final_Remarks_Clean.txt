We thank all reviewers for their evaluation. Our rebuttals successfully addressed concerns, with Reviewer VUC8 raising their score to 4 and all reviewers converging on 4: Borderline Accept.

Key Achievements

FedFree achieves 46.3% accuracy improvement over state-of-the-art while being the first privacy-preserving heterogeneous FL framework requiring no proxy datasets.

Core Innovations: (1) Data-free knowledge transfer using Gaussian pseudo-data eliminates privacy risks while enabling effective heterogeneous aggregation. (2) Knowledge Gain Entropy (KGE) provides principled selective alignment, ensuring beneficial updates only.

Reviewer Validation

VUC8 (raised to 4): Accepted KGE validation and privacy analysis. TinyImageNet results showed broad applicability.

wr5p (4): Convinced by scalability and efficiency demonstrations. ResNet-50 experiments confirmed performance gains.

fz64 (4): Satisfied with theoretical justifications and robustness across complex datasets.

BXtX (4): Agreed with knowledge sharing focus vs. architectural adaptation approach.

Technical Strengths

FedFree solves the privacy-performance trade-off through: (1) O(1/T) convergence guarantees, (2) Privacy-by-design eliminating proxy dataset vulnerabilities, (3) >80% communication reduction via selective layer transfer.

Unlike existing methods requiring shared data, FedFree enables truly decentralized learning across diverse devices—critical for healthcare, IoT, and mobile applications.

Significance

FedFree represents a paradigm shift toward practical, privacy-preserving heterogeneous FL. Unanimous reviewer acceptance (all 4s) confirms this addresses a critical gap with robust theoretical foundations and exceptional empirical performance, enabling broader FL adoption in privacy-sensitive domains.
