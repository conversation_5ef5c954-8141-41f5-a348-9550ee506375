\documentclass[11pt]{article}
\usepackage[margin=1in]{geometry}
\usepackage{amsmath}
\usepackage{amsfonts}
\usepackage{amssymb}
\usepackage{graphicx}
\usepackage{cite}
\usepackage{url}
\usepackage{hyperref}
\usepackage{enumitem}

\title{Author Final Remarks\\
\large Submission 15480: FedFree: Breaking Knowledge-sharing Barriers through Layer-wise Alignment in Heterogeneous Federated Learning}

\author{Haizhou Du, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>}

\date{\today}

\begin{document}

\maketitle

We sincerely thank all reviewers for their constructive feedback. We are pleased that our comprehensive rebuttals have addressed major concerns, as evidenced by positive reviewer responses and score improvements (all reviewers rated 4: Borderline Accept).

\section*{Key Contributions}

FedFree introduces two fundamental innovations:
\begin{enumerate}[leftmargin=*]
\item \textbf{Reverse Layer-wise Knowledge Transfer}: Novel data-free local-to-global knowledge aggregation using Gaussian pseudo-data, eliminating proxy dataset reliance while preserving privacy.
\item \textbf{Knowledge Gain Entropy (KGE)}: Novel metric for targeted global-to-local knowledge alignment, enabling personalized updates based on quantifiable knowledge gain.
\end{enumerate}

\textbf{Strong Results}: Up to 46.3\% relative accuracy improvement over state-of-the-art baselines across CIFAR-10/100 and TinyImageNet with diverse architectures (ResNet18-152, GoogleNet, MobileNetV2).

\section*{Reviewer Concerns Addressed}

\textbf{Reviewer VUC8}: Clarified KGE as effective heuristic validated empirically. Provided TinyImageNet experiments and privacy analysis.

\textbf{Reviewer wr5p}: Demonstrated lightweight KGE computation with memory comparisons. Extended experiments with larger models confirmed scalability.

\textbf{Reviewer fz64}: Provided theoretical justification and additional TinyImageNet validation demonstrating robustness.

\textbf{Reviewer BXtX}: Clarified baseline selection rationale - FedFree focuses on knowledge sharing across existing heterogeneous models rather than architectural transformation.

\section*{Technical Strengths}

\textbf{Theoretical}: Rigorous $O(1/T)$ convergence guarantees under convex/non-convex settings.

\textbf{Privacy}: Inherent preservation through no proxy datasets, Gaussian pseudo-data with no client information, and no direct model sharing.

\textbf{Efficiency}: Communication overhead reduction by uploading only $k=2$ critical layers vs. full models.

\textbf{Generality}: Architecture-agnostic design adaptable to CNNs, ResNets, and Transformers.

\section*{Conclusion}

FedFree makes significant contributions through novel technical innovations, substantial empirical improvements, theoretical guarantees, and practical advantages. The positive reviewer feedback validates our work's technical soundness and significance for heterogeneous federated learning.

\end{document}
