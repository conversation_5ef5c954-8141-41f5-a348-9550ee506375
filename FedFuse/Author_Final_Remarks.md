We thank all reviewers for their evaluation. Our rebuttals successfully addressed concerns, with **all four reviewers converging on 4: Borderline Accept**, validating our work's technical soundness.

## Key Achievements

**FedFuse achieves significant performance improvements** over state-of-the-art in heterogeneous federated learning across CIFAR-100, TinyImageNet, and Flowers102 with up to 500 clients.

**Core Innovations**: (1) **Expert-guided Fusion** - First dynamic MoE-based knowledge fusion on client logits, enabling adaptive aggregation preserving personalized knowledge. (2) **Selective Knowledge Distillation** - Novel reverse KL strategy allowing selective global knowledge integration while preserving local features.

## Reviewer Validation

**ceRT** (4): Acknowledged timely problem, clear writing, rigorous experiments, and theoretical analysis. Appreciated transformer experiments and heterogeneous validation.

**kjvn** (4): Recognized non-trivial MoE+distillation design, extensive empirical results, and convergence theory. Confirmed rebuttal addressed public dataset concerns.

**r41q** (4): Praised novel methodology integrating dynamic fusion with selective distillation and comprehensive validation. Acknowledged filtering strategy responses.

**Z5Bu** (4): Appreciated relevant challenge, MoE integration approach, 500-client scalability, and consistent improvements. Validated dataset handling clarifications.

## Technical Strengths

**Theoretical**: O(1/T) convergence analysis for convex/non-convex cases adapting federated optimization for MoE-based fusion and selective distillation.

**Privacy**: Enhanced through logits-only sharing (no raw data/gradients), server-side isolation, and minimal information exposure vs. traditional FL.

**Scalability**: Effectiveness across diverse architectures (ResNet, GoogleNet, MobileNetV2) and up to 500 clients with competitive efficiency.

## Significance

FedFuse addresses critical heterogeneous FL limitations through unique adaptive fusion and selective distillation synergy. Unlike static aggregation approaches risking personalization loss, our framework dynamically preserves client-specific knowledge while enabling effective collaboration.

Unanimous reviewer acceptance (all 4s) confirms FedFuse's significant contributions through novel innovations, strong empirical validation, and theoretical guarantees, advancing heterogeneous federated learning for practical edge deployment.
