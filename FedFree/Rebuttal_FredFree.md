# FedFree：Breaking Knowledge－ sharing Barriers through Layer－wise Alignment in Heterogeneous Federated Learning 

Haizhou Du（／profile？id＝～Haizhou＿Du2），<PERSON><PERSON>（／profile？id＝～Yiran＿Xiang1）， <PERSON><PERSON>（／profile？id＝～Yiwen＿Cai1），<PERSON><PERSON><PERSON>（／profile？id＝～Xiufeng＿Liu3）， <PERSON><PERSON><PERSON>（／profile？id＝～Zonghan＿Wu1），<PERSON><PERSON>（／profile？id＝～Huan＿Huo1）， Guo<PERSON> Long（／profile？id＝～Guodong＿Long1）

10 May 2025 （modified： 28 May 2025）
NeurIPS 2025 Conference Submission
Conference，Senior Area Chairs， Area Chairs，Reviewers，Authors

Revisions（／revisions？id＝G10Y4vrhGF）
CC BY 4.0
（https：／／creativecommons．org／licenses／by／4．0／）
Keywords：Heterogeneous Federated Learning，Public－Data－Free，Knowledge Gain Entropy

## Abstract：

Heterogeneous Federated Learning（HtFL）enables collaborative learning across clients with diverse model architectures and non－IID data distributions，which are prevalent in real－world edge computing applications．Existing HtFL approaches typically employ proxy datasets to facilitate knowledge sharing or implement coarse－grained model－level knowledge transfer．However， such approaches not only elevate risks of user privacy leakage but also lead to the loss of fine－grained model－specific knowledge，ultimately creating barriers to effective knowledge sharing．To address these challenges，we propose FedFree，a novel data－free and model－free HtFL framework featuring two key innovations．First，FedFree introduces a reverse layer－wise knowledge transfer mechanism that aggregates heterogeneous client models into a global model solely using Gaussian－based pseudo data，eliminating reliance on proxy datasets．Second，it leverages Knowledge Gain Entropy（KGE）to guide targeted layer－wise knowledge alignment，ensuring that each client receives the most relevant global updates tailored to its specific architecture．We provide rigorous theoretical convergence guarantees for FedFree and conduct extensive experiments on CIFAR－10 and CIFAR－100．Results demonstrate that FedFree achieves substantial performance gains，with relative accuracy improving up to 46．3\％over state－of－the－art baselines．The framework consistently excels under highly heterogeneous model／data distributions and in large scale settings．

Checklist Confirmation：－I confirm that I have included a paper checklist in the paper PDF．
Supplementary Material： $\boldsymbol{⿻ 上 丨}$ zip（／attachment？id＝G10Y4vrhGF\＆name＝supplementary＿material）
Reviewer Nomination：© duhaizhou＠shiep．edu．cn

Responsible Reviewing: © We acknowledge the responsible reviewing obligations as authors.
Primary Area: General machine learning (supervised, unsupervised, online, active, etc.)
LLM Usage: © Not used at all (you can then skip the rest)
Declaration: (-) I confirm that the above information is accurate.
Submission Number: 15480

Filter by reply type...
Filter by author...
Search keywords...
Sort: Newest First

Everyone
Program Chairs
Submission15480...
Submission15480...
Submission15480...
17 / 17 replies shown
Submission15480...
Submission15480...
Submission15480...
Submission15480...
Submission15480...

Add:
Withdrawal
Author Final Remarks

## Official Review of <br> Submission15480 by Reviewer VUC8

Official Review by Reviewer VUC8
03 Jul 2025, 13:45 (modified: 05 Aug 2025, 10:38)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer VUC8
Revisions (/revisions?id=pOk6oY3DPk)
Summary:
In this paper, the author proposes FedFree, an innovative framework that can address the knowledge sharing barriers in heterogeneous federated learning (HiFL). This framework introduces a reverse layer-by-layer knowledge transfer mechanism. The server generates Gaussian pseudo-data and uses the gradient descent algorithm to minimize the output difference between the key client layer and the global layer on the pseudo-data, achieving knowledge sharing from local to global. The framework also innovatively uses knowledge gain entropy to quantify the difference in knowledge density between the corresponding global layer and the local layer, in order to identify the global layers with potential to enhance the client model. The author demonstrated that the convergence rate of FedFree is $\mathrm{O}(1 / \mathrm{T})$ under both strongly convex and non-convex Settings, and verified on the CIFAR dataset that FedFree significantly improves its performance relative to the baseline in heterogeneous scenarios and demonstrates robustness at different non-IID levels.

## Strengths And Weaknesses:

Strengths:

1. The article innovatively proposes a framework for agent-free data, avoiding the need for real agent datasets by generating Gaussian pseudo-data.
2. The article innovatively describes the role of FedFree from two directions: local $\rightarrow$ global and global $\rightarrow$ local.
3. The article provides detailed proofs of theories such as lemma, which are highly reliable.

Weaknesses:

1. As stated in Article 3.5, "Our central hypothesis is that a global layer having been updated using knowledge aggregated from multiple diverse clients, is likely to develop a weight distribution that encodes a broader range of features." and in practice, it is assumed that higher entropy corresponds to more diverse information, without providing proof or research status. It has certain limitations.
2. The article is only verified on the CIFAR-10/100 dataset. It is hoped that more experimental data can be added to prove the applicability of the FedFree framework.
3. The article points out that agentless data can avoid some privacy issues, but there is insufficient discussion on privacy security issues. Some aspects such as how using this framework can prevent the leakage of privacy issues or resist attacks can be added to illustrate that proposing this new framework has solved some privacy problems.
Quality: 2: fair
Clarity: 3: good
Significance: 2: fair
Originality: 2: fair
Questions:
Please refer to weaknesses.

## Limitations:

Please refer to weaknesses.
Confidence: 4: You are confident in your assessment, but not absolutely certain. It is unlikely, but not impossible, that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work.
Ethical Concerns: NO or VERY MINOR ethics concerns only
Paper Formatting Concerns:
None.
Code Of Conduct Acknowledgement: Yes
Responsible Reviewing Acknowledgement: Yes

## Rebuttal by Authors

Rebuttal
by Authors ( $\mathbf{(}$ ) Guodong Long (/profile?id=~Guodong_Long1), Xiufeng Liu (/profile?id=~Xiufeng_Liu3), Yiwen Cai (/profile?id=~Yiwen_Cai1), Huan Huo (/profile?id=~Huan_Huo1), +3 more (/group/info? id=NeurIPS.cc/2025/Conference/Submission15480/Authors))
㐁 31 Jul 2025, 13:17 (modified: 31 Jul 2025, 21:55)

Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Revisions (/revisions?id=oYKX5N9jaI)
Rebuttal:
We sincerely thank Reviewer VUC8 for their time, thorough review, and constructive feedback on our submission.

R1-1 (KGE Hypothesis): As stated in Article 3.5, "Our central hypothesis is that a global layer having been updated using knowledge aggregated from multiple diverse clients, is likely to develop a weight distribution that encodes a broader range of features." and in practice, it is assumed that higher entropy corresponds to more diverse information, without providing proof or research status. It has certain limitations.

Re: We appreciate you highlighting this point for clarification. While a formal theoretical proof establishing a direct causal link between Shannon entropy of quantized weights and the richness or diversity of learned features is challenging and remains an open research problem in general deep learning theory, within FedFree, KGE is explicitly framed as a computationally efficient proxy (lines 202-208) to identify global layers that are promising candidates for enhancing client models. The intuition is that layers aggregated from diverse client information are likely to contain a wider variety of patterns. The empirical effectiveness of KGE in guiding beneficial knowledge transfer is strongly supported by the significant performance gains observed across all our experiments in Section 4 (e.g., Table 1, Figures A3-A6) and specifically validated through the ablation study in Figure 4(a). This empirical success validates its practical utility within our framework. We will strengthen the phrasing in Section 3.5 to explicitly emphasize that KGE serves as an effective heuristic, validated by extensive empirical results, rather than implying a formally proven theoretical equivalence.
Comparison of Different Entropy Methods (CIFAR-100, 100 clients)
Entropy Type Ours Tsallis Entropy Collision Entropy
Accuracy (\%) 49.2045 .9844 .46
Table: Performance comparison of different entropy approaches on CIFAR-100 with 100 clients.
R1-2 (Limited Experimental Data): The article is only verified on the CIFAR-10/100 dataset. It is hoped that more experimental data can be added to prove the applicability of the FedFree framework.

Re: We acknowledge this valid point. CIFAR-10 and CIFAR-100 are standard and widely recognized benchmarks for FL, particularly for evaluating architectural heterogeneity. Our experiments already employ a wide range of complex CNN architectures (ResNet18-152, GoogleNet, MobileNetV2, etc., detailed in Table A1). To further demonstrate the applicability of FedFree, we have conducted additional experiments on the TinyImageNet dataset, which is significantly more challenging due to its higher resolution, increased number of classes (200), and greater data complexity.

The results, presented below, demonstrate that FedFree consistently outperforms baseline methods on this more complex dataset, further validating its generalizability. We emphasize that FedFree is model-agnostic and data-agnostic by design, relying on a layer-wise knowledge-sharing strategy that does not assume any
specific model architecture or input modality.
TinyImageNet Benchmark (100 clients)

| Method | Accuracy (\%) |
| :--- | ---: |
| Ours | 23.31 |
| FedProto | 16.67 |
| FedKD | 13.28 |
| FedGH | 17.72 |
| FedTGP | 15.35 |

Table: Comprehensive performance comparison showing absolute accuracy and relative improvements over baseline methods.

R1-3 (Privacy Discussion): The article points out that agentless data can avoid some privacy issues, but there is insufficient discussion on privacy security issues. Some aspects such as how using this framework can prevent the leakage of privacy issues or resist attacks can be added to illustrate that proposing this new framework has solved some privacy problems.

Re: We appreciate the emphasis on privacy, which is a critical concern in FL. We would like to clarify and highlight that a key strength of FedFree is its inherently privacy-preserving design due to its "data-free" and "model-free" nature of knowledge sharing (lines 8, 46, 112).

Specifically, FedFree avoids privacy leakage in the following ways:

1. No Proxy Dataset Reliance: Unlike many existing HtFL methods (e.g., FedMD, FedDF, FedKD discussed in lines 79-84) that rely on shared proxy datasets, FedFree completely eliminates this requirement, mitigating known privacy risks associated with data exposure.
2. Gaussian-based Pseudo-data: Knowledge sharing (local-to-global and global-to-local) is facilitated solely by server-generated Gaussian-based pseudo-data ( $X \sim \mathcal{N}(0, I)$ ) (lines 11, 51, 168--174). As stated in line~174, "this server-generated $X$ contains no client-specific information, thereby preserving privacy." This pseudo-data serves as a universal probe without seeing or inferring real client data.
3. No Direct Model Sharing/Averaging: FedFree does not directly share or average full model parameters across clients, which could expose architectural details or combined gradients. It only transfers functional knowledge of selected critical layers.

While privacy preservation is an inherent benefit of our design, rather than the primary focus of designing new privacy techniques, FedFree can be readily integrated with widely adopted mechanisms such as Differential Privacy (DP-FL), Secure Aggregation, or Homomorphic Encryption to further enhance its privacy guarantees. We will include an extended discussion on these inherent benefits and potential integrations in the Conclusion (Section 5, around lines 343-344) of the camera-ready version.

## Replying to Rebuttal by Authors

## Official Comment by Reviewer VUC8

Official Comment by Reviewer VUC8 蘦 05 Aug 2025, 10:37
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Comment:
Thank you for your response. I have no further conerns, and I will raise the score to 4.

## Replying to Rebuttal by Authors

## Mandatory Acknowledgement by Reviewer VUC8

Mandatory Acknowledgement by Reviewer VUC8 哑 05 Aug 2025, 10:38
© Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Mandatory Acknowledgement: I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/ (https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/)

## Official Review of Submission15480 by Reviewer wr5p

Official Review by Reviewer wr5p
02 Jul 2025, 10:56 (modified: 24 Jul 2025, 16:35)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer wr5p
Revisions (/revisions?id=Tw9NVpsyS5)
Summary:

The paper proposes FedFree, a data-free and model-free heterogeneous federated learning algorithm. In FedFree, each client uploads the top-k layers with the largest local update magnitudes (measured by the $L_{2}$ norm) to the server. To avoid privacy leakage caused by using proxy datasets, the server generates Gaussian-based pseudo-data, eliminating the need for real data. The authors introduce a novel metric called Knowledge Gain Entropy (KGE) and use it to identify and deliver the most informative layer from the global model to each client, thereby achieving more accurate knowledge alignment. Theoretical analysis shows that FedFree achieves a convergence rate of $\frac{1}{T}$ under both strongly convex and non-convex settings. Extensive experiments demonstrate that FedFree outperforms existing homogeneous and heterogeneous federated learning methods.

## Strengths And Weaknesses:

## Strengths:

1. The paper presents a clear and well-structured narrative that is easy to follow.
2. It provides a sound theoretical convergence analysis.
3. The proposed method effectively avoids privacy leakage caused by using proxy datasets.

## Weaknesses:

1. In the Targeted Knowledge Distribution module (Section 3.5), each client must traverse all layers of the global model to identify the optimal one, which incurs significant computational and time costs. Moreover, the global model used in experiments is a shallow network with only five layers, raising concerns about the scalability of the method to larger models commonly used in real-world applications.
2. The method does not consider the common federated learning setting where only a subset of clients participate in each training round.
3. The homogeneous models used for comparison in the experiments are overly simplistic with only five layers.
4. The paper lacks comparative experiments with algorithms related to Architectural Adaptation and Matching, as mentioned in the related work.

Quality: 2: fair
Clarity: 3: good
Significance: 2: fair
Originality: 3: good

## Questions:

1. In the Critical Layer Selection module(Section 3.3), wouldn't layers with more parameters naturally have an advantage due to their larger L2 norms? Why does this selection strategy empirically improve performance despite this bias? Would it be more reasonable to normalize the L2 norm by the number of parameters in each layer during computation?
2. In the Handling Dimensional Mismatches module(Section 3.5), when a client's corresponding layer has more parameters than the global model's, the excess parameters are simply zeroed out. Wouldn't this lead to potential information loss? Does this not affect the algorithm's performance? If not, what is the underlying reason for this robustness?
3. In the experiments, the test accuracy of FedAvg appears significantly lower. Is this because it was also limited to only 200 training rounds? If so, it may not have fully converged. Would it be more appropriate to compare the final test accuracy after convergence for all methods?

## Limitations:

Yes.
Rating: 4: Borderline accept: Technically solid paper where reasons to accept outweigh reasons to reject, e.g., limited evaluation. Please use sparingly.
Confidence: 3: You are fairly confident in your assessment. It is possible that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work. Math/other details were not carefully checked.
Ethical Concerns: NO or VERY MINOR ethics concerns only
Paper Formatting Concerns:
No formatting issues.
Code Of Conduct Acknowledgement: Yes
Responsible Reviewing Acknowledgement: Yes

## Rebuttal by Authors

Rebuttal
by Authors ( $\mathbf{(}$ ) Guodong Long (/profile?id=~Guodong_Long1), Xiufeng Liu (/profile?id=~Xiufeng_Liu3), Yiwen Cai (/profile?id=~Yiwen_Cai1), Huan Huo (/profile?id=~Huan_Huo1), +3 more (/group/info? id=NeurIPS.cc/2025/Conference/Submission15480/Authors))

31 Jul 2025, 13:30 (modified: 31 Jul 2025, 21:55)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Revisions (/revisions?id=6sVum4XQWf)
Rebuttal:
Thanks for your comments.

## Weakness 1 (Computational Cost of KGE \& Global Model Size / Critical Layer Selection Bias)

Re: 1) Computational Cost of KGE \& Global Model Size: We clarify that the process in the "Targeted Knowledge Distribution" module (Section 3.5) does not require traversing all layers of the global model for every client. Instead, for each client $i$ that uploaded $k$ critical layers, the server only identifies the global layer $g_{j}^{*}$ that maximizes positive KGE with respect to any of its uploaded critical layers (lines 210--213). This means the comparison is performed only for these $k$ critical layers against relevant global layers, for $k=2$ (line 154 ), is a very small number. The computational cost of KGE (Shannon entropy of quantized weights, lines 199--200) and the associated forward passes for functional distillation are lightweight, involving only these selected layers. Thus, the computational overhead of KGE is negligible compared to local model training or full model inference. 2) Regarding the global model's shallow architecture (Table A1), it is designed as an "effective intermediary" (lines 161--165) whose architecture is chosen to encompass diverse client models,
not necessarily to be the largest model. The strength of FedFree lies in its layer-wise knowledge transfer and alignment, which scales differently than methods relying on full model aggregation. For larger client models, the principle of selecting $k=2$ critical layers (lines 153--154) and performing layer-wise aggregation and alignment remains efficient because the number of layers processed by the server remains constant and small. We will add a sentence in Section~3.5 or Appendix D. 1 to clarify the efficiency of KGE computation. 2) Critical Layer Selection Bias: We clarify that our selection criterion is not based on the absolute L2 norm of each layer's weights, but rather on the magnitude of parameter change ( $\boldsymbol{\Delta} \boldsymbol{\theta}$, Equation 2) across training rounds. This delta represents parameter evolution dynamics, not their static magnitude. We acknowledge that using the raw L2 norm might potentially favor layers with larger parameter counts. However, for the $k=2$ layers we select, the change in learning is the dominant factor for identifying valuable knowledge, and the variation in layer sizes for these critical layers has a limited impact on this selection. Our empirical results, particularly the ablation study in Figure 4(a), strongly validate that this simple approach significantly outperforms random layer selection. Nonetheless, we agree that introducing parameter-count-normalized L2 change could be a valid refinement, and we will include this discussion and corresponding ablation results in the camera-ready version to further strengthen the robustness and interpretability of the selection criterion.

## Weakness 2 (Subset Client Participation / Dimensional Mismatches \& Zero-initialization)

Re: We address both aspects:

1. Subset Client Participation: While our primary experiments focused on full client participation to isolate performance gains in HtFL, FedFree's design is inherently compatible with partial client participation. Since clients only upload $k$ critical layers (Algorithm 1, line 7) and the server processes information only from participating clients, the framework naturally accommodates varying numbers of clients per round without fundamental changes. The server aggregates knowledge from whoever participates and distributes relevant global layers back. This flexibility is an implicit advantage of FedFree's selective, layer-wise approach. We will add a sentence to Section 3.2 or Section 5 to explicitly mention this inherent compatibility.
2. Dimensional Mismatches \& Zero-Initialization: We clarify this in Section 3.5 (lines 224--226). When the global layer is larger than the local client layer ( $d_{g}>d_{i}$ ), only the excess global parameters (for $r>d_{\min }$ ) are effectively "zeroed out" in the sense that they are not directly mapped to the smaller local layer. Crucially, the local layer $\theta_{i}^{l}[r]$ for $r>d_{\text {min }}$ retains its local initialization to ensure stable model evolution (line 226). The key reason for robustness is that the local model continues training on its private dataset after receiving the global update (Algorithm 1, line 5). This continued local training allows the client model to adapt, learn, and potentially recover or re-learn any "lost" information in the unprojected dimensions, thereby integrating the received global knowledge while preserving its local integrity. FedFree's strong experimental results validate this robustness. The KGE-guided mechanism further ensures that only layers with verifiable knowledge gain are selected, minimizing the risk of detrimental "information loss".

## Weakness 3 (Simplistic Homogeneous Models / FedAvg Convergence)

## Re: We address both concerns:

1. Simplistic Homogeneous Models: We acknowledge this comment. The homogeneous FL (HmFL) experiments (lines 260--262) were included as standard baselines to isolate the impact of statistical heterogeneity and validate FedFree's KGE-based alignment when architectural differences are absent. The primary contribution and core evaluation of FedFree are in the Heterogeneous Federated Learning (HtFL) settings, where we indeed employ a wide range of complex and diverse architectures. As detailed in Table A1 and lines 253--255, clients in our HtFL experiments utilize models like ResNet18, ResNet34, ResNet50, ResNet101, ResNet152, GoogleNet, and MobileNetV2, which are far more complex than a five-layer CNN. Our significant performance gains are most pronounced in these complex HtFL settings (e.g., up to $46.3 \%$ over FedKD, Table 1). We will emphasize this distinction more clearly in Section 4.1.
2. FedAvg Convergence: We clarify that the lower performance of FedAvg in our experiments is not primarily due to insufficient training rounds. 200 communication rounds (line 274) is a widely accepted and common benchmark for evaluating FL algorithms, especially in communicationconstrained scenarios. All methods, including FedAvg, were evaluated under the exact same training conditions and communication budget for fair comparison. As shown in Figure A3--A6 (Appendix), the test accuracy of FedAvg often plateaus around the 50th communication round, well before the 200round limit, indicating it converges within this budget but to a lower performance due to fundamental incompatibilities with heterogeneous local models and non-IID data. The significant performance gains of FedFree (Table 1) demonstrate its superior efficiency and effectiveness in achieving higher accuracy within these practical communication constraints, which is a crucial advantage for real-world FL.

## Weakness 4 (Lack of Architectural Adaptation/Matching Comparisons).

Re: We acknowledge that Section 2 (lines 97-106) discusses architectural adaptation methods. Our selection of baselines (FedProto, FedKD, FedGH, FedTGP, described in lines 263-268) was deliberate. These methods represent the most prevalent and competitive knowledge transfer paradigms in HtFL, such as knowledge distillation, representation/prototype sharing, and shared global heads, which FedFree directly aims to improve upon. FedFree's core innovation lies in its data-free, layer-wise reverse knowledge transfer mechanism and KGE-guided alignment. This approach is fundamentally distinct from methods that perform direct architectural transformation, matching, or pruning of models to fit heterogeneous structures (e.g., FedMA, HeteroFL, pFedHR). FedFree focuses on knowledge sharing across existing heterogeneous models rather than adapting or matching their architectures. Therefore, the chosen baselines are the most relevant and direct comparisons for FedFree's unique knowledge transfer mechanism. We will add a sentence to Section 4.1 to clarify this baseline selection rationale.

R2-5: In the Targeted Knowledge Distribution module, each client must traverse all layers of the global model to identify the optimal one, which incurs significant computational and time costs.

Re: While each client does evaluate all global layers during the Targeted Knowledge Distribution phase, we conducted computational overhead experiments. The overhead is minimal due to the lightweight forward pass operations involved.

Table: Memory consumption comparison across different federated learning algorithms with 100 clients.

| Algorithm | Ours | FedProto | FedKD | FedGH | FedTGP |
| :--- | :--- | :--- | :--- | :--- | :--- |
| Memory | $2,045 \mathrm{MB}$ | 373 MB | $2,641 \mathrm{MB}$ | $2,022 \mathrm{MB}$ | $4,167 \mathrm{MB}$ |

R2-6: The method does not consider the common federated learning setting.
Re: We would like to clarify that our FedFree framework does simulate the common federated learning setting where only a subset of clients participate in each training round. Specifically, in all experiments, we select a fixed fraction of clients per round to perform local training and aggregation, consistent with standard FL protocols.

R2-7: The homogeneous models used for comparison in the experiments are overly simplistic with only five layers.

Re: We chose a 5-layer global model as it balances expressiveness and computational efficiency; excessively large server models can lead to unnecessary resource consumption and slower aggregation without significant accuracy gains. We conducted additional experiments with larger global models (e.g., ResNet-34 on CIFAR-100), where FedFree still demonstrates the similar performance and scalability.

Table: Performance comparison of different global model architectures. Results show classification accuracy.

| Model | Accuracy (\%) |
| :--- | ---: |
| CNN (5-Layer) | 49.20 |
| ResNet-18 | 49.11 |
| ResNet-34 | 46.73 |
| ResNet-50 | 44.20 |

Replying to Rebuttal by Authors

## Mandatory <br> Acknowledgement by Reviewer wr5p

Mandatory Acknowledgement by Reviewer wr5p 無 07 Aug 2025, 15:45
(-) Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Mandatory Acknowledgement: I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will
be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/ (https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/)

## Replying to Rebuttal by Authors

## Official Comment by Reviewer wr5p

Official Comment by Reviewer wr5p 洅 07 Aug 2025, 15:47
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors

## Comment:

I would like to thank the authors for the response, which addresses most of my concerns. However, the results on the more complex models remain unclear. Specifically, the accuracy of ResNet50 drops by 5\% as compared to the 5-layer CNN, which is counterintuitive. In addition, results of the other algorithms on these complex architectures are not provided. This raises concerns about the applicability of the proposed method to more mainstream or deeper models.

## Replying to Official Comment by Reviewer wr5p

## Our experimental results are intuitive.

## Official Comment

by Authors ( $\mathbf{(}$ ) Guodong Long (/profile?id=~Guodong_Long1), Xiufeng Liu (/profile?id=~Xiufeng_Liu3), Yiwen Cai (/profile?id=~Yiwen_Cai1), Huan Huo (/profile?id=~Huan_Huo1), +3 more (/group/info? id=NeurIPS.cc/2025/Conference/Submission15480/Authors))

## 䒼 09 Aug 2025, 13:54

(-) Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors

## Comment:

We appreciate the reviewer's insightful comments regarding the applicability of FedFree on the deeper and more mainstream model architectures. Our solution focuses on heterogeneous model aggregation to improve personalized model performance. Our experimental results are not counterintuitive. The previous observed accuracy dropping of ResNet50 was limited training budget (200 rounds), which is sufficient for the lightweight 5-layer CNN but not for larger model architectures that need to more rounds for convergence. At this time, We extend the training rounds until convergence for the same ResNet50 setting, and updated the results as follows.

## Global Model Architecture Comparison

Table: Performance comparison of different global model architectures. Results show classification accuracy.

| Model | Ours Acc. upon Convergence (\%) | Ours Convergence Round | FedGH Acc. |
| :--- | :--- | :--- | :--- |
| CNN (5-Layer) | 49.20 | 190 | 29.93 |
| ResNet-18 | 49.11 | 200 | 30.05 |
| ResNet-34 | 50.74 | 250 | 30.23 |
| ResNet-50 | 51.41 | 340 | 31.11 |

The above results demonstrate that FedFree not only supports but also benefits from deeper global models when is convergent. The FedFree's accuracy increases from 49.20\% (CNN) to 51.41\% (ResNet50) that demonstrate the result is intuitive for researchers.

Moreover, the FedFree is designed to reverse layer-wise knowledge transfer and pseudo-data-based knowledge alignment-scaling by KGE. Our framework focuses on identifying and transferring the critical knowledge from the selected key layers rather than the whole global model. The fine-grained public datafree knowledge alignment effectively decouples the framework performance from the global model's sizes.

Regarding the reviewer's concern about applicability to mainstream architectures, we emphasize that FedFree is inherently architecture-agnostic. The proposed reverse layer-wise knowledge transfer and Knowledge Gain Entropy mechanisms operate on abstracted model parameters and server-generated pseudo-data, making them independent of the specific neural architecture. This allows FedFree to adapt seamlessly to CNNs, ResNets, and potentially Transformers, without any architecture-specific modifications.

We will add these extended results and clarifications in the revised manuscript, which we believe address the reviewer's concerns about scalability and applicability to deeper models.

## Official Review of <br> Submission15480 by Reviewer fz64

Official Review by Reviewer fz64
30 Jun 2025, 12:46 (modified: 24 Jul 2025, 16:35)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer fz64
Revisions (/revisions?id=IKD6jzmy6b)

## Summary:

This paper proposed the FedFree framework to address the knowledge sharing barriers caused by different model architectures and non-independent and identically distributed data among participating clients in heterogeneous federated learning. Existing methods usually rely on proxy datasets for knowledge distillation, which poses risks of privacy leakage and difficulties in actual deployment. FedFree addresses this challenge through three core innovations.

First, a reverse hierarchical knowledge transfer mechanism was used to transfer knowledge from the client's key layer (identified by the magnitude of parameter changes) to the global model, and the server-generated Gaussian pseudo data was used to replace the real proxy data for functional matching. Then, the paper proposed the knowledge gain entropy (KGE) metric, which aims to evaluate the knowledge density of the global layer relative to the local layer by quantifying the Shannon entropy difference of the weight distribution. Finally, the paper proposed a selective knowledge alignment strategy to distribute only global layers with positive KGE values to clients, ensuring beneficial knowledge transfer. Experiments on CIFAR-10 and CIFAR-100 show that FedFree achieves performance improvements in various heterogeneous settings.

## Strengths And Weaknesses:

Strengths a) The paper effectively illustrates the fundamental challenges in heterogeneous federated learning, clearly distinguishing between local-to-global and global-to-local knowledge sharing barriers. The motivation for avoiding proxy datasets is well established by discussing privacy risks and practical deployment challenges. b) The three-module framework (knowledge extraction, knowledge sharing, knowledge alignment) provides a clear organizational structure, making complex methods easier to understand. c) FedFree's fine-grained hierarchical knowledge extraction and alignment approach provides a new level of accuracy for heterogeneous federated learning, better capturing subtle differences between client models. d) The paper introduces KGE as a quantitative measure of knowledge density, which is novel.

Weaknesses a) The contribution of the paper lies more in applying model alignment with synthetic data to heterogeneous federated learning rather than conceptual innovation. b) Although the theoretical analysis proves convergence, the proof ignores some of FedFree's unique algorithmic complexity innovations. c) Although the experimental design is relatively comprehensive, the test scope is still limited. It is only evaluated on two relatively simple computer vision datasets: CIFAR-10 and CIFAR-100. d) The paper lacks detailed analysis of computational complexity and communication overhead.

Quality: 3: good
Clarity: 3: good
Significance: 3: good
Originality: 2: fair
Questions:
a) Could you provide a more rigorous theoretical analysis to handle the convergence of the layer replacement operation and analyze the impact of the expected update frequency on convergence under the KGE selection mechanism? b) Can you provide the performance of FedFree on models with larger parameter sizes and more complex datasets? c) Can you provide a computational complexity analysis of FedFree and a comparison of the communication overhead with baseline methods? d) Please provide a more in-depth theoretical analysis to explain why reverse knowledge transfer is more effective than traditional methods and why Gaussian pseudo data can effectively capture hierarchical functions?

## Limitations:

The authors do honestly point out some important limitations, particularly the lack of real-world deployment and validation of large models.

Rating: 4: Borderline accept: Technically solid paper where reasons to accept outweigh reasons to reject, e.g., limited evaluation. Please use sparingly.
Confidence: 5: You are absolutely certain about your assessment. You are very familiar with the related work and checked the math/other details carefully.
Ethical Concerns: NO or VERY MINOR ethics concerns only
Paper Formatting Concerns:
N/A
Code Of Conduct Acknowledgement: Yes
Responsible Reviewing Acknowledgement: Yes

## Rebuttal by Authors

Rebuttal
by Authors (© Guodong Long (/profile?id=~Guodong_Long1), Xiufeng Liu (/profile?id=~Xiufeng_Liu3), Yiwen Cai (/profile?id=~Yiwen_Cai1), Huan Huo (/profile?id=~Huan_Huo1), +3 more (/group/info? id=NeurIPS.cc/2025/Conference/Submission15480/Authors))

31 Jul 2025, 13:35 (modified: 31 Jul 2025, 21:55)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Revisions (/revisions?id=zrgJNooRK6)
Rebuttal:
Thanks for your comments.
Weakness (a) (Contribution: Application vs. Innovation)
$\mathbf{R e}$ : We respectfully disagree with the assessment that our contribution is solely an application. FedFree introduces two core conceptual innovations that are distinct from existing model alignment or knowledge distillation methods:

1. Reverse Layer-wise Knowledge Transfer with Gaussian Pseudo-data: This is a novel mechanism (lines 9--12, 48--53) for data-free local-to-global knowledge aggregation. Unlike traditional knowledge distillation that often relies on shared proxy datasets, FedFree aggregates knowledge from diverse client models into a unified global representation solely using server-generated Gaussian-based pseudo-data (lines 168--174). This method of eliciting functional responses with a generic, privacy-preserving probe to bridge architectural differences for knowledge transfer is a significant conceptual departure.
2. Knowledge Gain Entropy (KGE): This is a novel metric (lines 12--14, 54--57, 65-68) designed for targeted global-to-local knowledge alignment. KGE quantifies the knowledge density difference between corresponding global and local layers. This fine-grained, adaptive distribution mechanism, which ensures personalized updates based on quantifiable knowledge gain, is a conceptual step beyond simple global model distribution or other adaptive methods, which lack such a precise, entropy-based selection criterion.

These innovations are not mere applications but fundamental design choices that enable FedFree to break knowledge-sharing barriers without reliance on real proxy data or the limitations of full model aggregation, as in Section 1. We will strengthen the Introduction (Section 1) and Conclusion (Section 5) to explicitly highlight these conceptual innovations and their distinct nature.

Weakness (b) \& Question (a) (Theoretical Analysis: Ignoring Algorithmic Complexity / More Rigorous Theoretical Analysis for Layer Replacement/KGE): Although the theoretical analysis proves convergence, the proof ignores some of FedFree's unique algorithmic complexity innovations. Could you provide a more rigorous theoretical analysis to handle the convergence of the layer replacement operation and analyze the impact of the expected update frequency on convergence under the KGE selection mechanism?

Re: We appreciate this feedback on our theoretical analysis. The theoretical convergence analysis (Section 3.6, Appendix B) provides rigorous guarantees for the overall objective function convergence of the FedFree framework. It adapts standard federated optimization techniques to specifically account for the "pseudo-data-based knowledge aggregation and KGE-guided alignment mechanisms unique to FedFree" (lines 239241).

While a formal, rigorous theoretical analysis that precisely quantifies the impact of the KGE selection mechanism on the expected update frequency of individual layers or the specific layer replacement operation on the overall convergence path is exceedingly complex and largely unexplored in the HtFL literature. This level of granular theoretical analysis would necessitate intricate modeling of layer-specific learning dynamics and their interaction with an entropy-based selection heuristic, likely moving beyond the scope of a single paper. Our empirical results (Section 4) strongly validate the practical effectiveness of these mechanisms in achieving superior performance and stable convergence. We will add a brief discussion in Appendix B or C to clarify the scope of our theoretical analysis, stating its focus on the overall framework's convergence while acknowledging the complexity of proving fine-grained mechanism impacts.

Weakness (c) \& Question b(Limited Test Scope/Datasets): The test scope is still limited, only evaluated on two relatively simple computer vision datasets: CIFAR-10 and CIFAR-100. Can you provide the performance of FedFree on models with larger parameter sizes and more complex datasets?

Re: We acknowledge this point. While CIFAR-10 and CIFAR-100 are widely used benchmark datasets, our HtFL experiments already utilize a wide range of complex and large models for clients, including ResNet18-152, GoogleNet, and MobileNetV2, with parameter sizes ranging from 3.2 M to 58.2 M (Table A1, lines 253-255). To further demonstrate generalizability, we conducted additional experiments on the TinyImageNet dataset, which is significantly more challenging (200 classes, higher resolution, greater complexity).

The results, presented in the table below, demonstrate that FedFree maintains strong performance and convergence behavior under these more demanding conditions, outperforming baselines. This shows our method's robustness to increased dataset complexity and larger model sizes.

Table: Detailed comparison showing absolute performance and relative resource differences.

## Comprehensive Benchmark on TinyImageNet (100 clients)

| Metric | Ours | FedProto | FedKD | FedGH | FedTGP |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Accuracy | $23.31 \%$ | $16.67 \%$ | $13.28 \%$ | $17.72 \%$ | $15.35 \%$ |
| Memory | $2,045 \mathrm{MB}$ | 373 MB | $2,641 \mathrm{MB}$ | $2,022 \mathrm{MB}$ | $4,167 \mathrm{MB}$ |

## Weakness (d) \& Question (c) (Lack of Computational Complexity \& Communication Overhead Analysis)

Re: You are absolutely correct that a detailed analysis of computational complexity and communication overhead would strengthen the paper. We acknowledge this omission and propose to add a dedicated subsection in the Appendix (e.g., D.6) or the main paper to provide this analysis:

Computational Complexity: Client-side: This involves local training (the standard FL cost) and the computation of the L2-norm for all layers, followed by the selection of the top- $k$ layers, which incurs negligible overhead. Server-side: The server performs operations including the generation of Gaussian pseudo-data (negligible cost), computation of the distillation loss for the $k$ selected global layers (Equation 3), and calculation of KGE (Equation 6) for these $k$ layers. These are efficient operations, whose cost is proportional to $k$ and the size of the selected critical layers, rather than the full model. The global layer $g_{j}^{t}$ is updated in a single gradient descent step (Equations 4 and 5) per communication round, making it efficient compared to iterative optimization.

Compared to methods that aggregate full models or perform complex model matching, FedFree's serverside computation is efficient due to its targeted, layer-wise nature.

Communication Overhead: FedFree provides a significant advantage by uploading only $k$ critical layers (line 155), rather than transmitting the entire local model as in FedAvg or sharing full model parameters or intermediate features. For instance, if a client model comprises $L$ layers and $k=2$ layers are selected (line 154), the communication cost is reduced by a factor of $L / 2$ compared to uploading the full model. This reduction is particularly beneficial in communication-constrained federated learning. The precise savings will be quantified in an added section by comparing the number of parameters transferred.

Moreover, we monitored the GPU memory consumption and training latency during our experiments on TinyImageNet (see Table below for example placeholders). FedFree consistently demonstrates modest memory overhead and efficient training latency, confirming its practical viability.

## Comprehensive Benchmark on TinyImageNet (100 clients)

| Metric | Ours | FedProto | FedKD | FedGH | FedTGP |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Accuracy | $23.31 \%$ | $16.67 \%$ | $13.28 \%$ | $17.72 \%$ | $15.35 \%$ |
| Memory | $2,045 \mathrm{MB}$ | 373 MB | $2,641 \mathrm{MB}$ | $2,022 \mathrm{MB}$ | $4,167 \mathrm{MB}$ |

## Question (d) (Why Reverse Knowledge Transfer and Gaussian Pseudo-data are Effective)

Re: We can provide a deeper explanation for their effectiveness based on the functional and informationtheoretic principles:

1. Effectiveness of Reverse Knowledge Transfer (Local-to-Global): Reverse knowledge transfer (lines 48--50) is effective because it directly aggregates the most significant learning from diverse local models (the critical layers identified by large parameter changes, lines 149--154) into a unified global representation. This process is functional rather than structural, meaning it captures what the layers have learned rather than how they are structured. By distilling knowledge via functional responses elicited by pseudo-data (Equation~3), FedFree can effectively bridge architectural differences. The server's global model learns to mimic the output behavior of the critical local layers, enabling knowledge transfer without direct parameter averaging (invalid for heterogeneous architectures) or reliance on shared proxy datasets. It directly addresses the "local-to-global knowledge-sharing barrier" (lines 39--40).
2. Effectiveness of Gaussian Pseudo-data: Gaussian pseudo-data (lines 51, 168--174) is effective because it provides a "diverse set of input signals capable of eliciting varied functional responses from neural network layers" (lines 170--172). It makes no assumptions about the true data distribution and introduces no data-dependent privacy risks. It acts as a "universal probe" that, despite its simplicity, is sufficient to activate various feature detectors and functional pathways within a neural network layer. This allows for a functional comparison and distillation between heterogeneous layers in a privacypreserving manner. The empirical success of FedFree (Section 4, particularly Figure 4(b)'s ablation study) strongly validates its utility in capturing and transferring functional knowledge across layers.

We will add a more explicit explanation of the "universal probe" concept to Section 3.4 to further clarify the role of Gaussian pseudo-data.

## Replying to Rebuttal by Authors

## Official <br> Comment by Reviewer fz64

Official Comment by Reviewer fz64 僼 04 Aug 2025, 23:39
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Comment:
Thanks the authors for the rebuttal. After reading the rebuttal and feedback from other reviewers, I decide to maintain my previous score.

Replying to Rebuttal by Authors
Mandatory
Acknowledgement
by Reviewer fz64

## Mandatory Acknowledgement by Reviewer fz64 囸 04 Aug 2025, 23:39 <br> Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors

Mandatory Acknowledgement: I have read the author rebuttal and considered all raised points., I have engaged in discussions and responded to authors., I have filled in the "Final Justification" text box and updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-AC Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to possibly LLM-generated reviews)., I understand my Review and my conduct are subject to Responsible Reviewing initiative, including the desk rejection of my co-authored papers for grossly irresponsible behaviors. https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/ (https://blog.neurips.cc/2025/05/02/responsible-reviewing-initiative-for-neurips-2025/)

## Official Review of Submission15480 by Reviewer BXtX

(https://openreview.net/forum?id=G10Y4vrhGF\&noteId=RcxfHK5xkr)

Official Review by Reviewer BXtX 雷 25 Jun 2025, 08:48 (modified: 04 Aug 2025, 11:32)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors, Reviewer BXtX
Revisions (/revisions?id=RcxfHK5xkr)

## Summary:

This paper proposes FedFree, a model-heterogeneous federated learning method based on knowledge transfer without shared data. FedFree introduces a reverse layer-wise knowledge transfer mechanism and a Knowledge Gain Entropy metric for data-free knowledge transfer between clients and servers. Theoretical analyses and experimental results validate the effectiveness of FedFree.

## Strengths And Weaknesses:

## Strengths:

1. The paper addresses an important and timely problem in FL. Enabling model-heterogeneous FL has the potential to greatly improve the applicability and flexibility of FL in real-world scenarios.
2. The motivation is clear, and the overall writing quality is good, making the manuscript easy to follow.
3. The experimental setup appears to be highly reproducible, with hyperparameters and implementation details well documented.

## Weaknesses:

1. The proposed "Critical Layer Selection" seems to essentially perform layer-wise top- $k$ sparsification. This makes me wonder: since FedFree is motivated by enabling model-heterogeneous FL, what is the rationale for incorporating communication-efficient techniques? Why not simply transferring all layers (rather than only the top- $k$ )?
2. In the knowledge distillation stage, the method samples $\hat{X}$ from a standard multivariate Gaussian distribution. Does this implicitly assume that the aggregate data distribution across all clients is well-approximated by a standard Gaussian? If not, how does the method perform if this assumption is violated?
3. What is the computational complexity of the "Reverse Knowledge Transfer" step? Is it $O\left(L^{2}\right)$, where $L$ is the number of layers?
4. The layer-wise distillation strategy in "Reverse Knowledge Transfer" could potentially cause the "short-sight" issue, where each individual layer is optimized without full awareness of the global objective. What are the authors' thoughts or potential remedies regarding this issue?
5. In Line 194, can the authors empirically or theoretically validate the central hypothesis of the method?
6. The experiments are only conducted on CIFAR-10 and CIFAR-100 datasets with various CNN architectures. This experimental evaluation seems limited and may not fully capture the generality or robustness of the proposed approach.
7. In Algorithm 1, Line 12, how many iterations are required to optimize $g_{i}^{t}$ ? What criterion is used for termination of this optimization process?
8. Why is FedFree not compared to methods based on "Architectural Adaptation and Matching," which are also introduced in the related work section?
9. In Figure 4(b), it appears that FedFree and FedAvg achieve similar final convergence (though FedFree converges faster). Given the additional computational overhead in FedFree, it is questionable whether the proposed aggregation method offers a significant advantage over FedAvg.
Quality: 2: fair
Clarity: 3: good
Significance: 2: fair
Originality: 3 : good
Questions:
Please see Strengths and Weaknesses.

## Limitations:

Please see Strengths and Weaknesses.
Confidence: 4: You are confident in your assessment, but not absolutely certain. It is unlikely, but not impossible, that you did not understand some parts of the submission or that you are unfamiliar with some pieces of related work.
Ethical Concerns: NO or VERY MINOR ethics concerns only
Paper Formatting Concerns:
N/A
Code Of Conduct Acknowledgement: Yes
Responsible Reviewing Acknowledgement: Yes

Rebuttal by
(https://openreview.net/forum?id=G10Y4vrhGF\&noteId=cGsDKuDIff) Authors

## Rebuttal

by Authors ( $\mathbf{(}$ ) Guodong Long (/profile?id=~Guodong_Long1), Xiufeng Liu (/profile?id=~Xiufeng_Liu3), Yiwen Cai (/profile?id=~Yiwen_Cai1), Huan Huo (/profile?id=~Huan_Huo1), +3 more (/group/info? id=NeurIPS.cc/2025/Conference/Submission15480/Authors))

31 Jul 2025, 13:43 (modified: 31 Jul 2025, 21:55)
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors
Revisions (/revisions?id=cGsDKuDIff)
Rebuttal:
Thanks for your comments.

## Weakness 1 \& Question (Critical Layer Selection/Sparsification)

Re: We appreciate this important question about the rationale behind critical layer selection. The selection of k critical layers (line 153) is motivated by two primary objectives, not solely communication efficiency:

1. Effective Knowledge Extraction (Primary): Our core motivation is to capture the most significant learning updates from diverse client models (lines 149--154). The L2-norm of parameter change ( $\Delta \Theta$, Equation 2) serves as a robust indicator of which layers have undergone the most substantial and meaningful learning in a given round. By focusing on these "critical layers," FedFree ensures that the most impactful knowledge for the global model is extracted, rather than transmitting redundant or less relevant information. In HtFL, directly aggregating all layers is often impossible or less effective due to architectural mismatches, and transferring all local layers indiscriminately would dilute global learning by introducing uninformative or non-transferable updates. The top- $k$ strategy naturally enforces this selectivity.
2. Communication Efficiency (Beneficial Side Effect): While not the sole driver, the resulting reduction in communication overhead by transferring only $k$ layers (line 155) is indeed a significant practical benefit for resource-constrained FL environments.

Therefore, critical layer selection is fundamentally about identifying and transferring high-value knowledge in a targeted manner, which inherently aids in both effective HtFL and communication efficiency. The ablation study in Figure 4(a) empirically validates this, showing that selecting critical layers based on parameter change significantly outperforms random selection.
*Weakness 2 \& Question (Gaussian Pseudo-data Assumption)
Re: No, our method does not assume that the true client data distribution-or the aggregate thereofis Gaussian. The use of a standard multivariate Gaussian distribution ( $\mathrm{X} \sim \mathrm{N}(0, \mathrm{I})$, lines 168-174) in FedFree serves a fundamentally different purpose:

1. Neutral Probing Input: The Gaussian pseudo-data acts as a neutral probing input, allowing us to extract functional responses from both global and local critical layers under the same synthetic stimuli. Its purpose is to provide a "diverse set of input signals capable of eliciting varied functional responses from neural network layers without relying on any prior knowledge of the true data distribution or introducing data-dependent privacy risks" (lines 170--172).
2. Functional Alignment: The goal is to update global layers so that their outputs align with the functional behavior of local layers (i.e., reverse knowledge transfer), given a shared, architecture-agnostic input space. Because the pseudo-data is not required to resemble real data, it avoids privacy risks while still enabling meaningful activation-space alignment.

The empirical success of FedFree (Section 4, particularly Figure 4(b)'s ablation study) demonstrates that this simple, knowledge-agnostic probing mechanism is highly effective in facilitating functional alignment and knowledge transfer even in complex, non-IID settings.

## Weakness 3 \& Question (Computational Complexity of "Reverse Knowledge Transfer")

Re: The computational complexity of the "Reverse Knowledge Transfer" step (Knowledge-sharing Module, Section 3.4) at the server is not $\mathbf{O}\left(\mathbf{N}^{*} \mathbf{L}\right)$, where N is the number of layers. It is significantly more efficient due to its layer-wise and selective nature.

For each communication round:

1. Client-side: Clients perform local training (standard FL cost) and then calculate L2-norms of parameter changes for all their layers (negligible). They upload only k critical layers (typically $\mathrm{k}=2$ ).
2. Server-side: Operations include generating Gaussian pseudo-data (negligible cost). For each participating client (denote $C$ as the number of clients) and for each of their $k$ uploaded critical layers, the server identifies a corresponding global layer (an $\mathcal{O}(1)$ lookup), feeds pseudo-data through these two specific layers (one client layer and one global layer), and calculates the distillation loss (Equation 3). The global layer $g_{j}^{t}$ is updated in a single gradient descent step (Equations4 and5) using the server-side learning rate $\alpha$. This cost is proportional to $C \times k \times$ (computation per layer forward pass and loss), which is far less than processing all layers of all client models.

Therefore, the server-side computation is proportional to the number of participating clients multiplied by the number of critical layers ( $\mathrm{k}=2$ ) and the size of those specific layers, making it highly efficient. We will add a dedicated subsection in the Appendix (e.g., D.6) to provide a detailed computational complexity analysis.

## Weakness 4 \& Question ("Short-sight" issue in Layer-wise Distillation)

$\mathbf{R e}$ : We thank the reviewer for highlighting this important concern. While layer-wise distillation might theoretically introduce a "short-sight" issue, FedFree mitigates this through several mechanisms:

1. Global Model as an Integrating Intermediary: The global model (lines 161-165) integrates knowledge from diverse client-critical layers into a more holistic representation. The aggregation process (Equation 3) considers the functional response of the global layer to all critical client layers mapped to it, encouraging a generalized understanding.
2. Client-Side Full Model Training and Adaptation: After receiving an updated global layer, the client model continues its local training on its full private dataset and entire model architecture (Algorithm 1, line 5). This allows the client to integrate the global knowledge into its complete model structure, adapt it to its specific data, and ensure consistency with its overall local objective.
3. KGE-Guided Beneficial Transfer: The Knowledge Gain Entropy (KGE) mechanism (lines 210-214) explicitly ensures clients only receive global layers that offer positive knowledge gain, preventing the injection of
potentially detrimental updates.
These mechanisms ensure that while knowledge is transferred layer-wise, it is ultimately integrated into and validated by the full model's local training, mitigating the "short-sight" issue.

## Weakness 5 \& Question (KGE Central Hypothesis Validation)

Re: We appreciate you highlighting this key hypothesis. As stated in Section 3.5 (lines 194, 202-208), the idea that higher entropy reflects more diverse information is presented as a "central hypothesis" and "guiding principle," not a formal theoretical proof. Rigorous theoretical validation linking Shannon entropy of quantized weights to semantic knowledge diversity in deep neural networks remains an open and complex research challenge.

However, the empirical effectiveness of KGE as a "computationally efficient proxy" is strongly validated by our extensive experimental results in Section 4. Specifically, the ablation study in Figure 4(a) shows that selecting global layers based on positive KGE significantly outperforms random layer selection for global-tolocal knowledge distribution. This robust empirical performance across diverse heterogeneous settings strongly supports its practical utility within FedFree's framework for identifying beneficial knowledge. We will strengthen the phrasing in Section 3.5 to emphasize its empirical validation as a highly effective heuristic.

## Weakness 6 \& Question (Limited Experimental Evaluation)

Re: We acknowledge this point. While we utilized CIFAR-10 and CIFAR-100, which are standard and widely accepted benchmarks for federated learning, our HtFL experiments incorporate a wide range of complex and large CNN architectures (ResNet18-152, GoogleNet, MobileNetV2, with parameters up to ~58.2M, Table A1). To further demonstrate generalizability, we have conducted additional experiments on the TinyImageNet dataset, which is significantly more challenging (200 classes, higher resolution, greater complexity).

The results, presented in the table below, demonstrate that FedFree consistently outperforms baseline methods on TinyImageNet, further validating its generalizability and robustness under more demanding conditions. We emphasize that FedFree's core principles are designed to be model-agnostic and dataagnostic.
TinyImageNet Results (100 clients)

| Method | Accuracy (\%) |
| :--- | ---: |
| Ours | $\mathbf{2 3 . 3 1}$ |
| FedProto | 16.67 |
| FedKD | 13.28 |
| FedGH | 17.72 |
| FedTGP | 15.35 |

Table: Accuracy comparison showing absolute results and differences from our method.

## Question 7 (Figure 4(b) - FedFree vs. FedAvg in Ablation)

Re: We believe there might be a misunderstanding of Figure 4(b). This figure presents an ablation study of FedFree's internal knowledge-sharing mechanism. It explicitly compares "FedFree's pseudo-data knowledge sharing" (our proposed aggregation method) against :simple averaging of uploaded layers" (not FedAvg). The comparison demonstrates that our pseudo-data-based functional distillation (Equation 3) significantly outperforms a naive direct averaging of uploaded critical layers (even if feasible across architectures), highlighting the benefit of our functional knowledge distillation.

FedAvg typically performs much worse in HtFL settings due to architectural heterogeneity, as clearly shown in Table 1 (e.g., FedAvg achieves only $16.87 \%$ on CIFAR-100 with 100 clients, compared to FedFree's $49.20 \%$ ) and Figures A4-A6 (where FedAvg's performance is significantly lower than FedFree's and other HtFL baselines). FedFree offers substantial advantages over FedAvg in HtFL, achieving up to $46.3 \%$ accuracy improvement. We will refine the caption and discussion of Figure 4(b) to ensure its purpose as an internal ablation is perfectly clear.

## Replying to Rebuttal by Authors

## Official Comment by Reviewer BXtX

## Official Comment by Reviewer BXtX

04 Aug 2025, 11:32
Program Chairs, Senior Area Chairs, Area Chairs, Reviewers Submitted, Authors

## Comment:

Thank you for the authors' responses. My concerns have been mostly addressed, and I decide to increase my score to 4.

## Replying to Rebuttal by Authors

## Mandatory

Acknowledgement
About openkeview Beber ${ }^{\text {at) }}$
by Review (/
Contact (/contact)
Frequently Asked Questions
![](https://cdn.mathpix.com/cropped/2025_08_11_9b6803a002d07ce541cdg-24.jpg?height=78&width=1141&top_left_y=1606&top_left_x=505)
(https://docs.openreview.net/getting-
![](https://cdn.mathpix.com/cropped/2025_08_11_9b6803a002d07ce541cdg-24.jpg?height=66&width=503&top_left_y=1668&top_left_x=476)
rea Chairs, Area Chairs, Reviewers Submitted, Authors
started/frequently-asked-
![](https://cdn.mathpix.com/cropped/2025_08_11_9b6803a002d07ce541cdg-24.jpg?height=54&width=1576&top_left_y=1732&top_left_x=547) engaged in discussions and responded to authors., I have filled in the "Final Justificatiperrfoxtdaysed/legal/terms) updated "Rating" accordingly (before Aug 13) that will become visible to authors once decisions are released., I understand that Area Chairs will be able to flag up Insufficient Reviews during the Reviewer-Ą Discussions and shortly after to catch any irresponsible, insufficient or problematic behavior. Area Chairs will be also able to flag up during Metareview grossly irresponsible reviewers (including but not limited to

