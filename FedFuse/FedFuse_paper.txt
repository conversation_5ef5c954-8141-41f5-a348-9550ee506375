FedFuse: Selective Knowledge Distillation with
Expert-Guided Fusion for Heterogeneous Federated
Learning

Anonymous Author(s)

Abstract
Heterogeneous Federated Learning enables collaborative training across devices
with diverse architectures and non-IID data. However, it often struggles with
effective knowledge fusion, leading to the loss of personalized knowledge during
aggregation and potentially exacerbating client model divergence due to globallyguided updates misaligned with local data or architectures. To tackle the challenges,
we propose FedFuse, a novel framework centered on adaptive, personalized knowledge fusion via logits. FedFuse introduces a server-side Expert-guided Fusion
mechanism that uniquely facilitates adaptive knowledge fusion by dynamically
gating and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes. Complementarily, an elaborately designed selective
knowledge distillation strategy allows clients to assimilate global knowledge without blind imitation, thereby preserving crucial local model features and mitigating
detrimental model divergence. We provide rigorous convergence analysis for FedFuse under heterogeneity. Extensive experiments, including up to 500 clients,
diverse heterogeneity settings, and ablation studies validating the necessity of both
core components, demonstrate our superiority. FedFuse significantly outperforms
state-of-the-art methods in test accuracy, particularly under high heterogeneity,
while exhibiting competitive communication and computational efficiency.

1
2
3
4
5
6
7
8
9
10
11
12
13
14
15
16
17
18

19

20
21
22
23
24
25
26
27
28
29
30
31
32
33
34
35
36
37

1

Introduction

Heterogeneous Federated Learning (HeteroFL) presents a compelling paradigm for collaborative
machine learning in diverse edge computing environments, such as the Artificial Intelligence of Things
[40], smart surveillance [25], autonomous vehicles [23]. It uniquely accommodates the reality of
edge ecosystems where clients possess varying computational resources, data distributions (statistical
heterogeneity), and even distinct underlying model architectures (architectural heterogeneity) tailored
to local needs. While HeteroFL effectively leverages the collective knowledge of the heterogeneous
network, it introduces a critical issue: the loss of personalized knowledge. This phenomenon, where
discrepancies arise between local objectives and global aggregation due to client heterogeneity
significantly degrades the performance of personalized models.
While many approaches offer valuable contributions, two challenges remains. First, the key challenge
is how to effectively fuse heterogeneous client knowledge while preserving personalized features
during aggregation. Conventional methods [27, 41, 39, 12] typically produce a single homogenized
global representation, which is hard to differentiate and preserve personalized knowledge, particularly
for clients with divergent data distributions. Consequently, valuable knowledge is often diluted
or ignored, degrading the performance of fused global model. The second challenge is how to
align global knowledge with local model without disrupting locally learned features, essential for
maintaining personalized performance. Traditional methods enforce uniform updates that may
conflict with client heterogeneity, forcibly diverting the local model from its optimized state.
Submitted to 39th Conference on Neural Information Processing Systems (NeurIPS 2025). Do not distribute.

38
39
40
41
42
43
44
45
46
47

To tackle these intertwined challenges of adaptive aggregation and compatible personalization, we
propose FedFuse, a novel HeteroFL framework built upon two synergistic insights: leveraging expertguided fusion and utilizing selective knowledge distillation. The choice of an expert-guided fusion
mechanism for server-side knowledge fusion is motivated by its potential to handle heterogeneity
effectively, drawing inspiration from its success in large-scale modeling [29, 28]. We introduce a
server-side MoE operating on uploaded logits [28]. Unlike static aggregation, the MoE’s dynamic
gating network learns to route and weight knowledge contributions (represented by logits) from
different clients to specialized experts. This allows the server to adaptively capture relevant knowledge
patterns from subsets of clients, preserving personalized information while constructing a rich, diverse
global representation.

53

Furthermore, FedFuse incorporates a compatible selective knowledge distillation strategy. Instead of
blindly applying global updates, clients selectively integrate only the most relevant global knowledge,
determined by the alignment between local and global feature representations, minimizing negative
transfer and preserving crucial local features. This selective approach operates within the logits
space [9], further ensuring compatibility between local and global updates and mitigating the risk of
disrupting locally learned features.

54

The main contributions of this paper are therefore summarized as:

48
49
50
51
52

• We address the issue of personalized knowledge loss in HeteroFL by proposing FedFuse, a
framework enabling an expert-guided fusion and a selective knowledge distillation across
statistically and architecturally heterogeneous clients.
• We introduce a novel expert-guided fusion mechanism that, unlike prior methods, dynamically captures and fuses personalized knowledge from heterogeneous clients based on the
relevance between local knowledge and multi global experts, mitigating personalization loss
during aggregation.
• We mitigate local feature damage caused by blindly imitating the global model, and effectively reduces client heterogeneity by introducing a compatible selective knowledge
distillation strategy that effectively selects favorable global knowledge for local model
updates optimization to preserve key local model features.
• We conduct extensive empirical validation across diverse benchmarks (CIFAR-100, TinyImageNet, Flowers102), including large-scale scenarios with up to 500 clients and rigorous
ablation studies, demonstrating that FedFuse achieves significant accuracy improvements
over state-of-the-art HeteroFL methods, particularly under high heterogeneity, while maintaining competitive resource efficiency.

55
56
57
58
59
60
61
62
63
64
65
66
67
68
69
70

71

72
73
74
75
76
77
78
79
80
81
82
83
84
85
86
87
88
89
90

2

Related Work

Federated learning under statistical and architectural heterogeneity (HeteroFL) has garnered significant attention. Existing approaches attempt to bridge the gap between diverse clients and enable
collaborative training, primarily falling into three categories.
HeteroFL with Knowledge Distillation (KD). These methods [27, 42, 30, 34, 8, 16, 20, 18]
leverage knowledge distillation, where clients typically train local models and generate knowledge
representations (e.g., soft labels, feature maps) from their private data [13]. These representations are
aggregated by the server to guide the training of client models (students) or a global model, avoiding
direct parameter sharing. Examples include FedGKD [34] and FedIOD [8]. While effective for basic
knowledge fusion, a key limitation arises in personalization: the aggregation process often distills
knowledge into a single, potentially homogenized teacher model. This averaged knowledge may
struggle to adequately capture or fuse the specialized, personalized features required by clients with
highly diverse data distributions or functional roles within the HeteroFL network. The resulting
global guidance might not be optimally tailored for every unique client’s needs.
HeteroFL with Lightweight Representations. To reduce communication overhead and handle
architectural diversity, some methods employ lightweight representations instead of full model
parameters for aggregation. One line of work uses prototypes [4, 31, 39], where clients upload class
prototypes derived from their local data, which are then aggregated by the server. Another approach
involves sharing intermediate feature representations [11, 35, 37], allowing clients to contribute
learned features rather than parameters. While these methods significantly reduce communication
2

91
92
93
94
95
96
97
98
99
100
101
102
103
104

costs, relying on such simplified or aggregated representations (prototypes or features) carries the risk
of information bottleneck, potentially losing the fine-grained details crucial for deep personalization
on individual clients. It remains challenging for these compact representations to fully encapsulate
the diverse functionalities and specificities present across a truly heterogeneous client network.
HeteroFL with Model Transformation. This category focuses on aligning heterogeneous model
structures for aggregation. Some methods split models into shared components (e.g., feature extractors) and personalized components (e.g., predictors) [3, 24, 2, 26, 19, 12, 17]. Others attempt
to standardize heterogeneous architectures into a common format before aggregation or matching [5, 32, 33]. While enabling collaboration across different architectures, model transformation
often imposes structural constraints, such as requiring a uniform feature extractor dimension or
specific layer types. This can limit the flexibility needed for clients with genuinely distinct hardware
capabilities or highly specialized local tasks, potentially hindering optimal local adaptation and
personalization. The transformation or matching process itself might also inadvertently discard
valuable model-specific information pertinent to a client’s unique role.

117

In a nutshell, while prior research has made significant strides, existing paradigms often face difficulties in effectively balancing global knowledge fusion with local personalization. Methods based on
KD can risk generating overly generalized guidance, lightweight representations may lack sufficient
granularity for deep personalization, and model transformations can impose restrictive structural
constraints. These limitations hinder the ability to adaptively aggregate diverse personalized knowledge without significant information loss and to subsequently compatibly disseminate relevant global
insights without disrupting local model specialization. These challenges collectively contribute to
the persistent problem of client heterogeneity. To overcome these specific shortcomings, FedFuse
introduces a different approach. Our expert-guided fusion mechanism directly tackles the adaptive
fusion challenge by dynamically identifying and weighting relevant client knowledge via expert gates,
moving beyond simplistic averaging or static representations. Moreover, the compatible personalization challenge is addressed by our selective knowledge distillation strategy, specifically designed to
integrate this tailored global knowledge while respecting local model integrity and specificity.

118

3

Methodology

119

3.1

Problem Formulation

120

Consider a federated learning setting with K clients, indexed by i = 1, . . . , K. Each client i
possesses a private dataset Dir , which typically exhibits non-IID characteristics across clients, leading
to statistical heterogeneity. Furthermore, each client maintains a local model Mi , parameterized by
θi . These models Mi can vary significantly in terms of architecture, depth, or capacity (architectural
heterogeneity), reflecting diverse device capabilities and local requirements. The overarching goal is
collaborative training to enhance each client’s personalized model performance on its own data, rather
than converging to a single global model. Formally, this personalized objective can be conceptualized
as minimizing a collective loss function over the private datasets:

105
106
107
108
109
110
111
112
113
114
115
116

121
122
123
124
125
126
127

min

{θi }K
i=1

128
129
130
131
132
133
134
135
136
137

K
X

pi Fi (θi ),

where Fi (θi ) = E(x,y)∼Dir [L(Mi (θi ; x), y)].

(1)

i=1

Here, L is a suitable loss function (e.g., cross-entropy), Mi (θi ; x) is the prediction of client i’s
model, and pi is a weighting factor (e.g., proportional to |Dir | or 1/K). While this provides a general
objective, our framework focuses on the mechanisms of knowledge exchange to improve each θi
rather than directly optimizing this specific sum.
A core challenge in HeteroFL is how to effectively fuse heterogeneous client knowledge preserving
personalized features during aggregation. The averaged fusion of global knowledge frequently leads
to dilution or loss of valuable information, consequently degrading the performance of integrated
global knowledge. Access to a small, publicly available dataset Db is often assumed, as in prior
work [34], to facilitate model-agnostic knowledge fusion (e.g., via logits) without compromising data
privacy, which is a key enabler for our proposed mechanisms.
3

Client 1

Client 2

①
⑧
①
⑧

③

Server

..
.

② Raw Global Logits
④
Fusion Loss

..
.

⑤

Global Model
Feature
Extraction
Part

Expert 1
Expert 2
Gate

①
⑧

...

k

...

Client i

Expert 3
Expert 4

..
.

⑦

⑥

Fused Global Logits

Expert n

Feature
Classification
Part
output

MoE

1 ⃝
2 After local training on private data, clients generate
Figure 1: Overview of the FedFuse architecture. ⃝,
3
knowledge representations (logits cti ) using a public dataset and upload them. ⃝On
the server, the global MoE
4 ⃝
5 These global logits (ctg ) and the received client logits (cti ) are inputs
model produces its current logits ctg . ⃝,
6 The updated global MoE model
to the fusion loss La . This loss updates the global MoE model’s parameters. ⃝
7 ⃝
8 These refined logits c̃tg are distributed back to
generates refined global logits c̃tg from the public dataset. ⃝,
the clients, who use them to perform a personalized local model update via selective knowledge distillation.
138

3.2

139

158

To address the intertwined challenges of adaptive aggregation and compatible personalization in
HeteroFL, we propose FedFuse. The overall architecture, illustrated in Figure 1, orchestrates a
cyclical flow of knowledge: from clients to a central server for fusion, and then back from the
server to clients for personalized guidance. This framework comprises three main stages within each
communication round: (1) Client Local Training and Knowledge Representation: Clients first
perform local training on their private data Dir . They then use their updated models to compute
output logits (knowledge representations) on the public dataset Db . These model-agnostic logits,
capturing the client’s current knowledge state, are sent to the server. (2) Expert-guided Fusion:
The server employs a Mixture-of-Experts (MoE) mechanism operating on the received client logits.
This mechanism includes a global server model with a feature extractor and an MoE layer. A gating
network within the MoE layer dynamically selects and weights relevant experts to process features
from Db , aiming to produce aggregated global logits that adaptively fuse the diverse knowledge from
heterogeneous clients. This server model is trained using the client-provided logits. (3) Selective
Knowledge Distillation: After the server model is updated, it generates a refined set of global
logits. The server distributes these fused global logits back to the clients. Each client then uses these
global logits to guide the update of its local model parameters (θi ), employing a specialized loss
function (reverse KL divergence) designed to integrate global insights while preserving local model
specificity. This design aims to achieve effective knowledge sharing tailored to diverse client needs
and data, thereby mitigating detrimental model divergence often exacerbated by client heterogeneity
and enhancing personalized model performance.

159

3.3

140
141
142
143
144
145
146
147
148
149
150
151
152
153
154
155
156
157

160
161
162

The Overview of FedFuse Framework

Client Local Training and Knowledge Representation

In communication round t, each participating client i (from a selected subset Nt ⊆ {1, . . . , K}) first
updates its local model parameters θit using its private data Dir . This local training typically involves
multiple steps of gradient descent:
h
i
θit ← θit−1 − η∇θt−1 E(x,y)∼Dir Lc (fθt−1 (x), y) ,
i

163
164
165
166

i

(2)

where fθi is the forward pass of model Mi , Lc is the cross-entropy loss, η is the local learning rate.
θit is client i’s parameters at round t.
Subsequently, to prepare knowledge for server aggregation, client i uses fθit to compute output logits
on the public dataset Db . The raw output logits cti,l from client i for input x at round t:
cti,l = fθit ∈ RC ,
4

(3)

167
168

where C is the number of classes in Db . To stabilize the numerical range and potentially soften the
distribution for better knowledge fusion (akin to KD), we apply temperature scaling:
!
cti,l (x)
t
,
(4)
ci = softmax
τ

171

where τ is the temperature parameter (e.g., τ = 2.0 is used in our experiments, following common
practice in KD). Note that cti is a probability distribution. The set of logits {cti |x ∈ Db } for client i is
then uploaded to the server.

172

3.4

169
170

173
174
175
176
177
178
179

Expert-guided Fusion

Upon receiving the logits {cti }i∈Nt from participating clients, the server employs an MoE mechanism
to adaptively fuse this knowledge. The server maintains a global model Mg , whose primary role here
is to learn the fusion function. We structure the first fully connected layer(s) of Mg as the MoE layer.
Let θgt denote the parameters of the global model at round t. For an input x ∈ Db , let ψθgt (x) ∈ Rdin
be the output of the feature extractor ψθgt of Mg . ψθgt (x) is fed into the MoE layer, which consists of
E parallel experts (ej : Rdin → RC ) and a gating network g(·) : Rdin → RE . The gating network
computes scores for each expert based on the input features:
g(ψθgt (x)) = Wg ψθgt (x) + bg ,

180
181
182
183
184

185
186
187

where Wg ∈ RE×din and bg ∈ RE are parameters of the gating network. To achieve sparse
activation, which is often preferred in MoE for efficiency and specialization, we employ a top-k
gating strategy(top-k sensitivity experiments in the Appendix E). The gate selects the set E(x)
containing the indices of the k experts with the highest scores in g(ψθgt (x)), where k ≪ E. The
routing weights πj are then computed via softmax over the scores of the selected experts:

 P exp(gj (ψθgt (x)))
, if j ∈ E(x),
t (x)))
l∈E(x) exp(gl (ψθg
πj (ψθgt (x)) =
(6)
0,
otherwise.
This dynamic, input-dependent selection allows the model to route different inputs from Db to
potentially different subsets of experts, enabling specialized knowledge processing. Each selected
expert j ∈ E(x) processes the input features:
ej (ψθgt (x)) = Wj ψθgt (x) + bj ,

188
189

(5)

(7)

where Wj ∈ RC×din , bj ∈ RC are the parameters of expert j. The final MoE output (pre-activation
global logits) is a weighted combination of the outputs from the selected experts:
X
MoE(ψθgt (x)) =
πj (ψθgt (x))ej (ψθgt (x)).
(8)
j∈E(x)

190

191
192
193
194
195

Similar to the client-side processing, we apply temperature scaling to get the global logits:
!
MoE(ψθgt (x))
t
cg = softmax
.
τ

(9)

The core idea is to train the global MoE model (θg ) such that its output distribution ctg optimally
reflects a fusion of the class distributions {cti }i∈Nt . We achieve this by minimizing the average
Kullback-Leibler (KL) divergence [22] from the client logits to the global logits over the public
dataset and participating clients. The choice of DK (P ∥ Q) aims to find a Q (global logits) that is
close to the average of P s (client logits) in terms of information content. The fusion loss is:
X X
1
La =
τ 2 DK (cti ∥ ctg ).
(10)
b
|Nt ||D |
b
i∈Nt x∈D

196

The KL divergence [10] is calculated as:
DK (P ∥ Q) =

C
X
c=1

5

Pc log

Pc
.
Qc

(11)

Algorithm 1 Expert-Guided Fusion
Require: Current global model θgt−1 , local client logits {cti }i∈Nt , public dataset Db , server epochs
Es , global learning rate ηg .
1: Expert-Guided Fusion:
2: Let θgt−1 represents the global model parameters at round t − 1.
3: for server epoch e = 1, . . . , Es do
4:
Compute global logits ctg = {ctg (x)|x ∈ Db } via Equation 9 using θgt−1
5:
Calculate fusion loss La via Equation 10 using {cti }i∈Nt and ctg
6:
Update θgt−1 using Equation 12: θgt ← θgt−1 − ηg ∇θgt−1 La
7: end for
8: Let θgt−1 ← θgt
9: Compute updated global logits c̃tg = {c̃tg |x ∈ D b } via Equation 13 using θgt
10: return c̃tg , θgt

197

The server updates the global model parameters θg using gradients from this aggregation loss:
θgt ← θgt−1 − ηg ∇θgt−1 La ,

(12)

199

where ηg is the server learning rate. Note that the server only needs the client logits cti , not the client
model parameters θit . This part is shown in Alogorithm 1.

200

3.5

198

201
202

203
204
205
206
207

Selective Knowledge Distillation

After updating the global MoE model to θgt , the server uses it to generate a refined set of global logits
c̃tg for distribution back to the clients. These are computed using the updated parameters θgt on Db :
!
t (x))
MoE(ψ
θ
g
c̃tg = softmax
.
(13)
τ
Each client i receives this set of global logits c̃tg = {c̃tg |x ∈ Db }. The client then performs a
local update step aimed at incorporating the global knowledge encoded in c̃tg while retaining its
personalized features. This is achieved by minimizing a loss function that encourages the client’s
logits cti (computed using its current parameters θit ) to align with the received global logits c̃tg . We
employ the reverse KL divergence for this purpose:
Lp =

1 X
DK (c̃tg ∥ cti ).
|Db |
b

(14)

x∈D

208
209
210
211
212

The choice of reverse KL divergence DK (Q ∥ P ) is intentional. Minimizing DK (Q ∥ P ) encourages
P to have high probability where Q has high probability, but allows P to maintain its own modes
(preserving personalization) where Q has low probability. This contrasts with minimizing the forward
KL divergence DK (P ∥ Q), which tends to force P to cover all modes of Q, potentially suppressing
P ’s unique features. This update step modifies the client parameters:
θit ← θit−1 − ηp ∇θt−1 Lp ,
i

(15)

214

where ηp is the learning rate for the personalization update. θit becomes the starting point for the next
communication round’s local training on Dir . The complete process is summarized in Algorithm 2.

215

3.6

216

In this subsection, we provide theoretical insights into the proposed FedFuse framework. We establish
convergence guarantees for the algorithm under standard assumptions, demonstrating its stability and
convergence properties in the HeteroFL setting. The detailed theorems, assumptions, and proofs for
the convergence analysis are presented in Appendix C.2.

213

217
218
219

Theoretical Analysis

6

221

Theorem 1. The strong convex cases. We hold the defined L, β, µ, γ in Assumption 1 to 4.FedFuse
satisfies:

222

1
ησ 2
[F (θ1 ) − F (θ∗ )] +
(L + β + γ).
2µηT
4µ
Theorem 2. The non-convex case. FedFuse satisfies:

220

E[F (θt ) − F (θ∗ )] ≤

E[∥F (θt )∥2 ] ≤

223

224
225
226
227
228
229
230
231
232
233
234
235
236

4

2
3ηLσ 2
(F (θ1 ) − F (θ∗ )) +
.
ηT (1 − ηL)
2 − ηL

(16)

(17)

Experiments

Datasets. We evaluate on three standard Computer Vision (CV) datasets: CIFAR-100 [14], TinyImageNet [15], and Flowers102 [7]. These datasets represent varying levels of complexity, category count,
and inter-class similarity (Flowers102 features fine-grained distinctions). For statistical heterogeneity
(Section 4.1.4), we use the Dirichlet distribution (α) to partition data among clients.
Model Architectures (HeteroFL Setting). To simulate architectural heterogeneity, we utilize a pool
of eight diverse model architectures ranging from CNNs to deep ResNets and efficient MobileNets, as
detailed in Appendix E, Table 7. For experiments with K clients, models are assigned cyclically from
this pool. For homogeneous baseline comparisons (HmFL), all clients use the Model 1 architecture.
Baselines. We compare FedFuse against two groups of baselines: HmFL Baselines (adapted for
personalization): FedAvg [21], Per-FedAvg [6], FedProx [38], FedPer [1]. These are evaluated in
the homogeneous setting to assess personalization capability without architectural heterogeneity.
HeteroFL Baselines: FedKD [13], FedProto [31], FedMRL [36], FedTGP [39]. These methods are
designed to handle architectural and statistical heterogeneity.

241

Implementation Details. All experiments are implemented using PyTorch 2.1.0 and conducted on
NVIDIA 4090D GPUs. Key hyperparameters (learning rates, epochs, batch sizes, MoE configuration,
etc.) are detailed in Appendix E, Subsection E.2. Unless otherwise specified, results are averaged
over 3 runs with different random seeds in different settings. Accuracy reported is the average test
accuracy across all participating clients on their local test sets after the final communication round.

242

4.1

243

4.1.1

244

We first compare the final personalized test accuracy of FedFuse against baselines under both
HmFL and HeteroFL settings across varying client numbers (K = 10, 50, 100, 500). The results are
summarized in Table 1 (for K = 10) and Tables 8, 9 in Appendix E (for K = 50, 100, 500).

237
238
239
240

245
246

Performance Evaluation
Personalized Accuracy Comparison

256

FedFuse consistently achieves higher average test accuracy compared to all baseline methods across
the three datasets and client scales in the HeteroFL setting. For instance, in the 10-client HeteroFL
scenario (Table 1), FedFuse surpasses the best performing baseline by substantial margins: ↑11.1%
on CIFAR-100, ↑46.7% on Tiny-Imagenet, and ↑26.7% on Flowers102. Similar significant gains are
observed for K = 50 and K = 100. Moreover, FedFuse’s advantage grows with data complexity.
For example, on CIFAR-100, FedFuse outperforms the second-best method by about 10%; on TinyImagenet, the improvement ranges from 20% to 40%; and on Flowers102, it ranges from 10% to 40%.
This stems from FedFuse’s use of the Mixture-of-Experts (MoE) architecture to decouple knowledge
rather than simply aggregating it. By doing so, the model can fully absorb knowledge from different
clients, giving FedFuse a significant edge in complex tasks.

257

4.1.2

258

Figure 3 presents the training curves (average test accuracy vs. communication rounds) for the HeteroFL setting. FedFuse generally demonstrates faster convergence compared to baselines, exhibiting
a rapid accuracy increase in the early training stages, followed by steady growth. It consistently
outperforms other methods throughout the training process, with its advantage often becoming more
pronounced in the later stages. For instance, observing the curves in Figure 3, on the Tiny-Imagenet
dataset with 50 clients (Figure 3e), FedFuse’s accuracy is significantly higher than others by the

247
248
249
250
251
252
253
254
255

259
260
261
262
263

Convergence Speed

7

Table 1: Comparison on three datasets with 10 clients. The best result is bold, the second is underlined.
Acc(%)
Algorithm
CIFAR-100
Tiny-Imagenet
Flowers102
FedAvg [21]
31.76
15.82
20.18
Per-FedAvg [6]
31.57
26.30
22.33
HmFL
FedProx [38]
31.78
16.70
19.06
FedPer [1]
39.94
31.12
44.31
FedFuse
43.71 (↑9.4%)
33.64 (↑8.1%)
49.16 (↑10.9%)
FedKD [13]
35.90
21.95
39.24
FedProto [31]
33.20
16.93
27.05
HeteroFL
FedMRL [36]
37.98
22.10
37.68
FedTGP [39]
32.71
20.34
41.39
FedFuse
42.22 (↑11.1%) 32.43 (↑46.7%) 52.46 (↑26.7%)
Table 2: Evaluation under non-IID Data (Dirichlet α). The best result is bold, the second is underlined.
Acc(%)
HeteroFL
(100 clients)

HeteroFL
(50 clients)

HeteroFL
(10 clients)

Algorithm
FedKD [13]
FedProto [31]
FedMRL [36]
FedTGP [39]
Ours
FedKD [13]
FedProto [31]
FedMRL [36]
FedTGP [39]
Ours
FedKD [13]
FedProto [31]
FedMRL [36]
FedTGP [39]
Ours

α=0.05
26.36
19.85
36.28
26.53
45.22
35.86
33.62
40.09
31.14
44.32
41.38
33.09
45.30
40.88
47.70

Cifar-100
α=0.1
16.75
18.75
23.97
16.70
31.90
30.37
25.61
30.91
25.93
36.53
35.90
33.20
37.98
32.71
42.22

α=0.5
10.88
11.18
14.01
11.11
21.03
10.24
13.39
12.76
13.64
21.52
23.35
17.12
24.06
20.28
27.70

Flowers102
α=0.05 α=0.1 α=0.5
21.74
18.42
9.5
23.81
19.90
12.53
20.95
26.87
13.07
23.68
21.78
13.85
43.85
30.56
19.06
17.82
26.71
12.93
12.74
14.30
24.65
20.44
26.32
12.90
12.59
27.87
14.33
53.98
39.48
23.67
40.73
39.24
22.91
28.39
27.05
11.65
39.26
37.68
22.23
47.36
41.39
26.08
61.27
52.46
36.07

268

middle of training and stabilizes later. Similarly, on the Flowers102 dataset with 100 clients (Figure 3i), it maintains the highest accuracy throughout, with its lead over other methods widening over
time. Overall, FedFuse exhibits excellent training speed and convergence properties in the HeteroFL
setting. The corresponding curves for the HmFL setting, showing similar trends of fast convergence
for FedFuse, are provided in Figure 4 (Appendix E).

269

4.1.3

270

275

Figure 6 illustrates how the final average accuracy of different HeteroFL algorithms scales as the
number of clients increases from K = 10 to K = 500. FedFuse exhibits greater robustness compared
to baselines. For instance, on CIFAR-100, while other algorithms show a decline in accuracy with
more clients, FedFuse exhibits a more gradual decrease. On Flowers102, FedFuse consistently
achieves the highest accuracy across all client numbers. This highlights the robustness and scalability
of FedFuse in federated learning scenarios.

276

4.1.4

277

285

We evaluate the impact of statistical heterogeneity using the Dirichlet distribution Dir(α) with
α ∈ {0.5, 0.1, 0.05}. Table 2 shows the performance under these conditions for K = 10, 50, 100.
FedFuse consistently achieves the highest accuracy across all settings, demonstrating superior
robustness. For instance, with 100 clients, FedFuse achieves the highest accuracy of 45.22% on
the CIFAR-100 dataset [for α = 0.05] and 43.85% on the Flowers102 dataset [for α = 0.05], both
markedly outperforming other algorithms. This suggests that FedFuse possesses enhanced robustness
and superior performance when dealing with non-IID data. Its relative advantage often widens under
higher heterogeneity (e.g., α = 0.05), supporting the hypothesis that adaptive MoE aggregation is
particularly beneficial when client data differs significantly.

286

4.2

287
288

To dissect the contribution of the key components of FedFuse, we perform ablation studies on
CIFAR-100 with K=50, α = 0.1. We compare the full FedFuse framework against several variants:

289

w/o MoE: Replaces MoE aggregation with simple averaging of client logits, but keeps the Lp update.

264
265
266
267

271
272
273
274

278
279
280
281
282
283
284

Scalability with Client Numbers

Robustness to Data Heterogeneity

Ablation Studies

8

 

     

 

 
    

 

    

    

    

 

    

    
    

    
    

    

 & , ) $ 5    

 7 L Q \  , P D J H Q H W

 ' D W D V H W V

 

    

 
    
    

    

    

    

 

 ) O R Z H U V   

     

 

 
    

     

 ) H G . '
 ) H G 3 U R W R
 ) H G 0 5 /
 ) H G 7 * 3
 2 X U V

  

 ' R Z Q O R D G   0 % 

 8 S O R D G   0 % 

     

 ) H G . '
 ) H G 3 U R W R
 ) H G 0 5 /
 ) H G 7 * 3
 2 X U V

  

(a) Upload

    

    
    

    

    

 & , ) $ 5    

    

 7 L Q \  , P D J H Q H W

 ' D W D V H W V

    

    

    

 ) O R Z H U V   

(b) Download

Figure 2: Communication Overhead with 10-Client HeteroFL. Illustrates model-size independence.
290
291
292
293
294
295
296

w/o Lp : Uses MoE aggregation, but removes the final personalization update step (Equation 15).
FedGen [41]: A baseline employs a global generator to aggregate logits and cross-entropy loss for
client updates (no MoE, no specific Lp ).
The results in Table 3 demonstrate the importance of both components. Removing either MoE or the
Lp update results in a noticeable performance drop of 0.56% and 7.2% percentage points, respectively,
compared to the full FedFuse. Both components are necessary to achieve the best performance,
outperforming the simpler LogitsAvg-KD (FedGen) baseline significantly. This validates the design
choices of using adaptive MoE for aggregation and the specific reverse KL loss for personalization.
Table 3: Ablation study on CIFAR100, 50 clients.

297

Table 4: Computation study on CIFAR100

Method Variant α = 0.1.

Accuracy (%)

Method

Avg. Client Time (s)

Server Time (s)

FedFuse (Full)
w/o MoE Aggregation
w/o Personalized Update Lp
FedGen [41]
FedKD [13]

36.53
35.97
29.33
14.92
30.37

FedFuse
FedKD
FedProto
FedTGP
FedMRL

1.98
5.15
1.18
1.56
1.16

1.76
11.01
40.33
42.87
8.56

Algorithm
Time Complexity

Table 5: Time Complexity Analysis
FedKD
FedProto
FedTGP
FedMRL
O(N × C) O(N × C) O(N × C 2 ) O(N × C)

Ours
O(N × C)

298

4.3

299

We analyze the resource usage of FedFuse compared to baselines, and we summarize the time
complexities in Table 5, which provides a detailed analysis in Appendix C.1.

300
301
302
303
304
305

Resource Overhead Analysis

Communication Overhead. We measure the total data transferred per round (client uploads +
server downloads) in Megabytes (MB). Since FedFuse only transmits logits, its communication
cost is independent of client model sizes. Figure 2 illustrates the upload cost specifically for K=10.
Compared to other knowledge-fusion methods, FedFuse’s communication is determined by |Db | × C.
We find that FedFuse requires lower communication volume than FedMRL and FedTGP.

309

Computation Overhead. We measure the average wall-clock time per round with 50 clients and
α = 0.1. Table 4 reports the average time from the client and the server side. Client time heavily
depends on Ec and local model complexity Li . Server time depends on Es and MoE complexity. We
observe that FedFuse’s server time is 1.76s, lower than the baselines.

310

5

306
307
308

311
312
313
314
315
316
317
318
319

Conclusion

In this work, we introduce FedFuse, a novel framework designed to tackle the critical issues of personalized knowledge loss in HeteroFL. By uniquely combining an Expert-guided Fusion mechanism
for adaptive knowledge aggregation with a compatible selective knowledge distillation strategy for
preserving personalization, FedFuse effectively addresses the limitations of prior methods. Extensive
experiments confirmed FedFuse’s significant accuracy improvements over state-of-the-art HeteroFL
baselines, particularly under high heterogeneity, while demonstrating favorable resource trade-offs.
While FedFuse’s effectiveness has been demonstrated in near-real-world settings, practical deployment on physical devices and in environments with extremely large models remains untested due to
resource constraints. We will conduct practical deployment in the future work.
9

320
321
322
323
324
325
326
327
328
329
330
331
332
333
334
335
336
337
338
339
340
341
342
343
344
345
346
347
348
349
350
351
352
353
354
355
356
357

References
[1] Manoj Ghuhan Arivazhagan, Vinay Aggarwal, Aaditya Kumar Singh, and Sunav Choudhary.
Federated learning with personalization layers.
[2] Jiangui Chen, Ruqing Zhang, Jiafeng Guo, Yixing Fan, and Xueqi Cheng. FedMatch: Federated learning over heterogeneous question answering data. In Proceedings of the 30th ACM
international conference on information & knowledge management, pages 181–190, 2021.
[3] Liam Collins, Hamed Hassani, Aryan Mokhtari, and Sanjay Shakkottai. Exploiting shared
representations for personalized federated learning. In International conference on machine
learning, pages 2089–2099. PMLR, 2021.
[4] Yutong Dai, Zeyuan Chen, Junnan Li, Shelby Heinecke, Lichao Sun, and Ran Xu. Tackling
data heterogeneity in federated learning with class prototypes. In Proceedings of the AAAI
Conference on Artificial Intelligence, volume 37, pages 7314–7322, 2023.
[5] Enmao Diao, Jie Ding, and Vahid Tarokh. HeteroFL: Computation and communication efficient
federated learning for heterogeneous clients. In 9th International Conference on Learning
Representations, ICLR 2021, 2021.
[6] Alireza Fallah, Aryan Mokhtari, and Asuman Ozdaglar. Personalized federated learning
with theoretical guarantees: A model-agnostic meta-learning approach. Advances in neural
information processing systems, 33:3557–3568, 2020.
[7] I Gogul and V Sathiesh Kumar. Flower species recognition system using convolution neural
networks and transfer learning. In 2017 fourth international conference on signal processing,
communication and networking (ICSCN), pages 1–6. IEEE, 2017.
[8] Xuan Gong, Shanglin Li, Yuxiang Bao, Barry Yao, Yawen Huang, Ziyan Wu, Baochang
Zhang, Yefeng Zheng, and David Doermann. Federated learning via input-output collaborative
distillation. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 38, pages
22058–22066, 2024.
[9] Jianping Gou, Baosheng Yu, Stephen J Maybank, and Dacheng Tao. Knowledge distillation: A
survey. International Journal of Computer Vision, 129(6):1789–1819, 2021.
[10] Geoffrey Hinton, Oriol Vinyals, and Jeff Dean. Distilling the knowledge in a neural network.
stat, 1050:9, 2015.
[11] Wenke Huang, Mang Ye, and Bo Du. Learn from others and be yourself in heterogeneous
federated learning. In Proceedings of the IEEE/CVF Conference on Computer Vision and
Pattern Recognition, pages 10143–10153, 2022.
[12] Jaehee Jang, Heoneok Ha, Dahuin Jung, and Sungroh Yoon. FedClassAvg: Local representation
learning for personalized federated learning on heterogeneous neural networks. In Proceedings
of the 51st International Conference on Parallel Processing, pages 1–10, 2022.
[13] Eunjeong Jeong, Seungeun Oh, Hyesung Kim, Jihong Park, Mehdi Bennis, and Seong-Lyun
Kim. Communication-efficient on-device machine learning: Federated distillation and augmentation under non-IID private data. arXiv preprint arXiv:1811.11479, 2018.

359

[14] Alex Krizhevsky, Geoffrey Hinton, et al. Learning multiple layers of features from tiny images.
2009.

360

[15] Yann Le and Xuan Yang. Tiny imagenet visual recognition challenge. CS 231N, 7(7):3, 2015.

361

[16] Daliang Li and Junpu Wang. FedMD: Heterogenous federated learning via model distillation.
arXiv preprint arXiv:1910.03581, 2019.

358

362
363
364
365
366
367
368
369
370

[17] Paul Pu Liang, Terrance Liu, Liu Ziyin, Nicholas B Allen, Randy P Auerbach, David Brent,
Ruslan Salakhutdinov, and Louis-Philippe Morency. Think locally, act globally: Federated
learning with local and global representations. arXiv preprint arXiv:2001.01523, 2020.
[18] Tao Lin, Lingjing Kong, Sebastian U Stich, and Martin Jaggi. Ensemble distillation for
robust model fusion in federated learning. Advances in neural information processing systems,
33:2351–2363, 2020.
[19] Chang Liu, Yuwen Yang, Xun Cai, Yue Ding, and Hongtao Lu. Completely heterogeneous
federated learning. CoRR, 2022.
10

371
372

[20] Yuhang Ma, Zhongle Xie, Jue Wang, Ke Chen, and Lidan Shou. Continual federated learning
based on knowledge distillation. In IJCAI, pages 2182–2188, 2022.

375

[21] Brendan McMahan, Eider Moore, Daniel Ramage, Seth Hampson, and Blaise Aguera y Arcas.
Communication-efficient learning of deep networks from decentralized data. In Artificial
intelligence and statistics, pages 1273–1282. PMLR, 2017.

376

[22] Radford M Neal. Bayesian methods for machine learning.

377

[23] Anh Nguyen, Tuong Do, Minh Tran, Binh X Nguyen, Chien Duong, Tu Phan, Erman Tjiputra,
and Quang D Tran. Deep federated learning for autonomous driving. In 2022 IEEE Intelligent
Vehicles Symposium (IV), pages 1824–1830. IEEE, 2022.

373
374

378
379
380
381
382
383
384
385
386
387
388
389
390
391
392
393
394
395
396
397
398
399
400
401
402
403
404
405
406
407
408
409
410
411
412
413
414
415
416
417
418
419
420
421
422

[24] Jaehoon Oh, SangMook Kim, and Se-Young Yun. FedBABU: Toward enhanced representation
for federated image classification. In International Conference on Learning Representations.
[25] Yiran Pang, Zhen Ni, and Xiangnan Zhong. Federated learning for crowd counting in smart
surveillance systems. IEEE Internet of Things Journal, 2023.
[26] Krishna Pillutla, Kshitiz Malik, Abdel-Rahman Mohamed, Mike Rabbat, Maziar Sanjabi, and
Lin Xiao. Federated learning with partial model personalization. In International Conference
on Machine Learning, pages 17716–17758. PMLR, 2022.
[27] Felix Sattler, Arturo Marban, Roman Rischke, and Wojciech Samek. CFD: Communicationefficient federated distillation via soft-label quantization and delta coding. IEEE Transactions
on Network Science and Engineering, 9(4):2025–2038, 2021.
[28] Noam Shazeer, Azalia Mirhoseini, Krzysztof Maziarz, Andy Davis, Quoc Le, Geoffrey Hinton,
and Jeff Dean. Outrageously large neural networks: The sparsely-gated mixture-of-experts
layer. arXiv preprint arXiv:1701.06538, 2017.
[29] Sheng Shen, Le Hou, Yanqi Zhou, Nan Du, Shayne Longpre, Jason Wei, Hyung Won Chung,
Barret Zoph, William Fedus, Xinyun Chen, et al. Mixture-of-experts meets instruction tuning:
A winning combination for large language models. In The Twelfth International Conference on
Learning Representations.
[30] Changlin Song, Divya Saxena, Jiannong Cao, and Yuqing Zhao. FedDistill: Global model distillation for local model de-biasing in non-iid federated learning. arXiv preprint arXiv:2404.09210,
2024.
[31] Yue Tan, Guodong Long, Lu Liu, Tianyi Zhou, Qinghua Lu, Jing Jiang, and Chengqi Zhang.
FedProto: Federated prototype learning across heterogeneous clients. In Proceedings of the
AAAI Conference on Artificial Intelligence, volume 36, pages 8432–8440, 2022.
[32] Hongyi Wang, Mikhail Yurochkin, Yuekai Sun, Dimitris Papailiopoulos, and Yasaman Khazaeni. Federated learning with matched averaging. In International Conference on Learning
Representations, 2020.
[33] Jiaqi Wang, Xingyi Yang, Suhan Cui, Liwei Che, Lingjuan Lyu, Dongkuan DK Xu, and
Fenglong Ma. Towards personalized federated learning via heterogeneous model reassembly.
Advances in Neural Information Processing Systems, 36, 2024.
[34] Dezhong Yao, Wanning Pan, Yutong Dai, Yao Wan, Xiaofeng Ding, Chen Yu, Hai Jin, Zheng
Xu, and Lichao Sun. FedGKD: Towards heterogeneous federated learning via global knowledge
distillation. IEEE Transactions on Computers, 2023.
[35] Liping Yi, Gang Wang, Xiaoguang Liu, Zhuan Shi, and Han Yu. FedGH: Heterogeneous
federated learning with generalized global header. In Proceedings of the 31st ACM International
Conference on Multimedia, pages 8686–8696, 2023.
[36] Liping Yi, Han Yu, Chao Ren, Gang Wang, Xiaoxiao Li, et al. Federated model heterogeneous
matryoshka representation learning. Advances in Neural Information Processing Systems,
37:66431–66454, 2024.
[37] Liping Yi, Han Yu, Zhuan Shi, Gang Wang, Xiaoguang Liu, Lizhen Cui, and Xiaoxiao Li.
FedSSA: Semantic similarity-based aggregation for efficient model-heterogeneous personalized
federated learning. In Kate Larson, editor, Proceedings of the Thirty-Third International
Joint Conference on Artificial Intelligence, IJCAI-24, pages 5371–5379. International Joint
Conferences on Artificial Intelligence Organization, 8 2024. Main Track.
11

423
424
425
426
427
428
429
430
431
432
433
434
435
436
437
438

[38] Xiaotong Yuan and Ping Li. On convergence of fedprox: Local dissimilarity invariant bounds,
non-smoothness and beyond. Advances in Neural Information Processing Systems, 35:10752–
10765, 2022.
[39] Jianqing Zhang, Yang Liu, Yang Hua, and Jian Cao. FedTGP: Trainable global prototypes with
adaptive-margin-enhanced contrastive learning for data and model heterogeneity in federated
learning. In Proceedings of the AAAI Conference on Artificial Intelligence, volume 38, pages
16768–16776, 2024.
[40] Xinqian Zhang, Ming Hu, Jun Xia, Tongquan Wei, Mingsong Chen, and Shiyan Hu. Efficient
federated learning for cloud-based aiot applications. IEEE Transactions on Computer-Aided
Design of Integrated Circuits and Systems, 40(11):2211–2223, 2020.
[41] Zhuangdi Zhu, Junyuan Hong, and Jiayu Zhou. Data-free knowledge distillation for heterogeneous federated learning. In International conference on machine learning, pages 12878–12889.
PMLR, 2021.
[42] Zhuangdi Zhu, Junyuan Hong, and Jiayu Zhou. Data-free knowledge distillation for heterogeneous federated learning. In International conference on machine learning, pages 12878–12889.
PMLR, 2021.

12

Appendix

439

440

A

Algorithm

Algorithm 2 FedFuse: Selective Knowledge Distillation with Expert-Guided Fusion
Require: Communication rounds T , client participation fraction p, private datasets {Dir }K
i=1 , public
0
dataset Db , initial client models {θi0 }K
,
initial
global
model
θ
,
local
epochs
E
,
server
epochs
c
g
i=1
Es , learning rates η, ηg , ηp , temperature τ .
1: for each round t = 1, 2, . . . , T do
2:
Select a subset of clients Nt ⊆ {1, . . . , K}, |Nt | = max(1, ⌊p · K⌋)
3:
Client Local Training and Knowledge Representation (in parallel for i ∈ Nt ):
4:
Let θi′ ← θit−1
5:
for local epoch e = 1, . . . , Ec do
6:
Update θi′ using Equation 2 on Dir
7:
end for
8:
Compute local logits cti = {cti (x)|x ∈ Db } via Equation 3 and Equation 4 using θi′
9:
Send cti to server
10:
Let θit ← θi′
11:
Call Algorithm 1 with θgt−1 , {cti }i∈Nt , Db , Es , ηg to get c̃tg and θgt
12:
Distribute c̃tg to clients in Nt
13:
Selective Knowledge Distillation (in parallel for i ∈ Nt ):
14:
Receive c̃tg
15:
Compute personalized loss Lp via Equation 14 using c̃tg and logits from current θit
16:
Update local model via Equation 15: θit ← θit−1 − ηp ∇θt−1 Lp
i
17: end for
T
18: return Final personalized client models {θiT }K
i=1 (or {θi }i∈NT if only a subset has the final
update)

441

B

442

Table 6 summarizes the main notations used throughout the paper.

443

C

444

This section provides supplementary details for the theoretical analysis discussed in Section 3.6.

445

C.1

446
447

As mentioned in the main text, the time complexity of FedFuse in each communication round t
involves several components:

448

1. Client-Side Computation:

449
450
451
452
453
454
455
456
457
458

Notation

Theoretical Analysis

Complexity Analysis Details

• Local Training: Each of the |Nt | participating clients performs Ec local epochs. Within
each epoch, it processes |Dir | samples. Let Li be the average complexity (forward +
backward pass) for one sample on client i’s model Mi . The total complexity for local
training across selected clients is approximately O(|Nt | · Ec · maxi (|Dir | · Li )).
• Logits Generation: Each client computes logits on the public dataset Db . This involves one
forward pass per sample. Complexity is O(|Nt | · |Db | · Lfi wd ), where Lfi wd is the forward
pass complexity.
• Personalization Update: Each client computes the loss Lp and performs one gradient
update. This involves one forward pass on Db and one backward pass. Complexity is
O(|Nt | · |Db | · Li ).
13

Table 6: Notations

Symbol
K
i
Nt
T
Dir
Db
Mi , θi
Mg , θg
fθi (·)
ψθg (·)
C
cti,l (x)
cti
ctg
c̃tg
τ
η, ηg , ηp
λ
Ec
Es
E
k
E(x)
πj (·)
ej (·)
din
Li
Leg
Lc
La
Lp
DK (P ∥ Q)
459
460
461
462
463
464
465
466
467
468

Description
Total number of clients.
Index for clients.
Set of clients participating in round t.
Total number of communication rounds.
The client i’s private dataset.
The public dataset shared across clients and server.
Local model and its parameters for client i.
Global MoE model and its parameters on the server.
Forward pass function for client model i.
Feature extractor part of the global model Mg .
Number of classes in the classification task (dimension of logits).
Raw output logits from client i for input x at round t.
Temperature-scaled logits (probability distribution) from client i for input x at round t.
Aggregated global logits from server MoE model for input x at round t.
Updated global logits distributed from server to clients at round t.
Temperature parameter for scaling logits.
Learning rates for local training, server aggregation, and local personalization update.
L2 regularization coefficient for local training.
Number of local training epochs per round.
Number of server training epochs per round.
The total number of experts in the MoE layer.
The number of active experts selected by the Top-k gating mechanism.
Set of indices for the top-k active experts for input x.
Gating weight for expert j.
Output function for expert j.
Input dimension for the MoE layer (output dimension of ψ).
Computational complexity of one forward/backward pass for client i.
Computational complexity of the global model’s feature extractor ψ.
Cross-Entropy loss function.
Server-side aggregation loss (based on KL divergence).
Client-side personalization loss (based on reverse KL divergence).
Kullback-Leibler divergence from distribution P to Q.

2. Server-Side Computation:
• Aggregation Training: The server performs Es epochs to update the global MoE model. In
each epoch, it processes |Db | samples. Let Lg be the complexity of the global model (Mg )
pass. Lg includes the feature extractor (ψ, complexity Leg ) and the MoE layer. The MoE
layer involves computing gating weights (O(din E)), selecting top-k experts, and computing
weighted expert outputs (O(kCdin ) for linear experts). The backward pass has similar
complexity. Total server training complexity is O(Es · |Db | · Lg ).
• Global Logits Generation: Computing c̃tg involves one forward pass over Db , complexity
O(|Db | · Lfg wd ).
3. Communication:

470

• Upload: |Nt | clients upload logits for |Db | samples, dimension C. Total size O(|Nt | · |Db | ·
C).

471

• Download: Server broadcasts global logits c̃tg to |Nt | clients. Total size O(|Nt | · |Db | · C).

469

472
473
474
475
476

Assuming Li ≈ Lfi wd + Lbwd
and Lg ≈ Lfg wd + Lbwd
g , and often Ec ≫ 1, Es ≥ 1. The overall
i
per-round complexity depends heavily on the relative sizes of datasets, local vs server epochs, and
model complexities. The simplified complexity O(N × C) mentioned in the original draft likely
refers only to the communication cost under specific assumptions and neglects computational costs,
which can be substantial, especially local training.
14

477

C.2

478

In this subsection, we provide a formal convergence guarantee for the proposed FedFuse framework
under standard assumptions commonly used in federated optimization.
Assumption 1 (Smoothness). F is L-smooth: for all vector θ1 and θ2 ,

479
480

Convergence Analysis of FedFuse

∥∇F (θ1 ) − ∇F (θ2 )∥ ≤ L∥θ1 − θ2 ∥.
481

482
483

Another form:
L
F (θ1 ) ≤ F (θ2 )+ < θ1 − θ2 , ∇F (θ2 ) > + ∥θ1 − θ2 ∥2 .
2
Assumption 2 (Convexity). Assume F is strongly convex with parameter µ. For any vector θ1 , θ2 ,
we have:
µ
F (θ1 ) ≥ F (θ2 )+ < θ1 − θ2 , ∇F (θ2 ) > + ∥θ1 − θ2 ∥2 .
2
Assumption 3 (Stochastic Gradient Variance Bounded).
E[∥∇F (θ; x, y) − ∇F (θ)2 ] ≤ σ 2 .

485

Assumption 4 (MoE Boundedness). The gating weights {πj } sum to 1 (over the selected k experts),
and each expert’s outputs and gradients are uniformly bounded.

486

C.3

487

We establish several lemmas to facilitate the convergence proofs.

484

Key Lemmas
2

488
489

Lemma 1. Assume Assumption 1, 2, 3 hold, if learning rate η ≤ 2µ
L3 . Then the local update on the
client side satisfies:
E[Lc (θit+1 )] ≤ E[Lc (θit )] − µ2 ηE[∥θit − θi∗ ∥2 ] +

η 2 Lσ 2
.
2

(18)

2

490

Lemma 2. Assume Assumption 1, 2, 3, 4 hold, if learning rate ηg ≤ 2µ
β 3 . Then:
ηg2 βσ 2
.
2

(19)

γηp2 σ 2
.
2

(20)

E[La (θgt+1 )] ≤ E[La (θgt )] − µ2 ηg E[∥θgt − θg∗ ∥2 ] +
491

Lemma 3. Assume Assumption 1, 2, 3, when ηp ≤ γ2 ,
E[∥θ̃it+1 ∥] ≤ E[Lp (θ̃it )] − µ2 ηp E[∥θ̃it − θ̃i∗ ∥2 ] +

492

Lemma 4. Assume Assumption 1m 3, it follows:
E[∇Lc (θit+1 )] ≤ E[∇Lc (θit )] − η(1 −

493

(21)

ηp2 Lσ 2
ηp L
)E[∥∇Lp (θ̃it )∥2 ] +
.
2
2

(22)

ηg2 Lσ 2
ηg L
)E[∥∇La (θgt )∥2 ] +
.
2
2

(23)

Lemma 5. Assume Assumption 1, 3, 4, it follows:
E[∇Lp (θ̃it+1 )] ≤ E[∇Lp (θ̃it )] − ηp (1 −

494

ηL
η 2 Lσ 2
)E[∥∇Lc (θit )∥2 ] +
.
2
2

Lemma 6. Assume Assumption 1, 3, it follows:
E[∇La (θgt+1 )] ≤ E[∇La (θgt )] − ηg (1 −

495

C.4

Convergence analysis for strongly convex case

496

Proof. We start by defining the combined loss function as:
F (θ) = Lc (θ) + La (θ) + Lp (θ).

497
498

(24)

Based on Lemma 1, Lemma 2, and Lemma 3, we can infer that the learning rates are equal, i.e.,
η = ηp = ηg . This allows us to combine the results from the individual lemmas.
15

499

First, we consider the sum of the expected differences in the loss function over all iterations:
T
X

T
X

500

η2 σ2 T
(L + β + γ).
2
t=1
t=1
(25)
This inequality provides a lower bound on the sum of the expected differences in the loss function.

501

Next, we observe that the sum of the expected differences can also be written as:

E[F (θt ) − F (θt+1 )] ≥ µ2 η

T
X

E[∥θit − θi∗ ∥2 + ∥θgt − θg∗ ∥2 + ∥θ̃it − θ̃i∗ ∥2 ] −

E[F (θt ) − F (θt+1 )] = E[F (θ1 ) − F (θT +1 )] ≤ F (θ1 ) − F (θ∗ ).

(26)

t=1

503

This follows from the fact that the expected value of the loss function at the final iteration is less than
or equal to the loss function at the initial iteration.

504

Combining the two inequalities, we obtain:

502

µ2 η

T
X

E[∥θit − θi∗ ∥2 + ∥θgt − θg∗ ∥2 + ∥θ̃it − θ̃i∗ ∥2 ] ≤ F (θ1 ) − F (θ∗ ) +

t=1
505
506

η2 σ2 T
(L + β + γ). (27)
2

This inequality provides a bound on the sum of the squared differences between the current parameters
and the optimal parameters.

510

Next, we use the strong convexity property of the individual loss functions to get:
µ t
∥θ − θi∗ ∥2 ≤ Lc (θit ) − Lc (θi∗ ).
(28)
2 i
µ t
∥θ̃ − θ̃i∗ ∥2 ≤ Lp (θ̃it ) − Lp (θ̃i∗ ).
(29)
2 i
µ t
∥θ − θg∗ ∥2 ≤ La (θgt ) − La (θg∗ ).
(30)
2 g
Summing these inequalities, we get:
µ t
[∥θ − θi∗ ∥2 + ∥θgt − θg∗ ∥2 + ∥θ̃it − θ̃i∗ ∥2 ] ≤ F (θt ) − F (θ∗ ).
(31)
2 i
This inequality provides a bound on the combined squared differences in terms of the combined loss
function.

511

Using this result, we can bound the sum of the expected differences in the loss function as:

507

508

509

T
X

E[F (θt ) − F (θ∗ )] ≤

t=1

ησ 2 T
1
[F (θ1 ) − F (θ∗ )] +
(L + β + γ).
2µη
4µ

(32)

512

Finally, dividing both sides by T , we obtain the desired result:

513

ησ 2
1
[F (θ1 ) − F (θ∗ )] +
(L + β + γ).
(33)
2µηT
4µ
This inequality provides a bound on the expected difference in the loss function at each iteration.

514

C.4.1

515

Proof. Firstly, we leverage the strong convexity of Lc with parameter µ. This implies that the gradient
norm has a lower bound as follows:
∥∇Lc (θit )∥ ≥ µ2 ∥θit − θi∗ ∥2 .
(34)
Next, by Assumption 3, we can get an upper bound for the expected squared gradient norm:
E[∥∇L(θ; x, y)∥2 ] ≤ L2 E[∥θ − θ∗ ∥] + σ 2 .
(35)

E[F (θt ) − F (θ∗ )] ≤

516

517

518
519

Proof of Lemma 1

Then, considering the update rule θit+1 = θit − η∇Lc (θit ) and the L-smoothness of Lc , we have the
following inequality:
L
Lc (θit+1 ) ≤ Lc (θit ) + ⟨θit+1 − θit , ∇Lc (θit )⟩ + ∥θit+1 − θit ∥2
(36)
2
η2 L
≤ Lc (θit ) − η∥∇Lc (θit )∥2 +
∥∇Lc (θit )∥2 .
(37)
2
16

520

Taking the expectation on both sides, we obtain:
η2 L
E[∥∇Lc (θit )∥2 ]
(38)
2
η 2 Lσ 2
≤ E[Lc (θit )] − µ2 ηE[∥θit − θi∗ ∥2 ] +
.
(39)
2
t
∗ 2
To ensure the desired inequality holds, we need the coefficient of E[∥θi − θi ∥ ] to be non-negative,
3
2
which leads to the condition µ2 − L2 η ≥ 0. Solving this inequality, we get η ≤ 2µ
L3 .
E[Lc (θit+1 )] ≤ E[Lc (θit )] − ηE[∥∇Lc (θit )∥2 ] +

521
522
523

524

C.4.2

525

Proof. We start by considering the gradient of the aggregated loss La for the server parameters θg . It
is known that:
E[∥∇La ∥2 ] ≤ β 2 E[∥θgt − θg∗ ∥2 ] + σ 2 .
(40)
This inequality provides an upper bound for the expected squared gradient norm of the aggregated
loss.

526

527
528

Proof of Lemma 2

532

Next, we analyze the update rule for θg . Given the update rule θgt+1 = θgt − ηg ∇La (θgt ) and the
β-smoothness of La , we have:
β
(41)
La (θgt+1 ) ≤ La (θgt ) + ⟨θgt+1 − θgt , ∇La (θgt )⟩ + ∥θgt+1 − θgt ∥2
2
ηg2 β
≤ La (θgt ) − ηg ∥∇La (θgt )∥2 +
∥∇La (θgt )∥2 .
(42)
2
Taking the expectation on both sides, we obtain:
ηg2 β
E[∥∇La (θgt )∥2 ]
(43)
E[La (θgt+1 )] ≤ E[La (θgt )] − ηg E[∥∇La (θgt )∥2 ] +
2
ηg2 βσ 2
≤ E[La (θgt )] − µ2 ηg E[∥θgt − θg∗ ∥2 ] +
.
(44)
2
To ensure the desired inequality holds, we need the coefficient of E[∥θgt − θg∗ ∥2 ] to be non-negative.

533

This leads to the condition µ2 −

534

C.4.3

535

Proof. We begin by applying the update rule and the smoothness property of the loss function.
Specifically, for the local update on the client side, we have:
γ
Lp (θ̃it+1 ) ≤ Lp (θ̃it ) + ⟨θ̃it+1 − θ̃it , ∇Lp (θ̃it )⟩ + ∥θ̃it+1 − θ̃it ∥2
(45)
2
ηp2 γ
≤ Lp (θ̃it ) − ηp ∥∇Lp (θ̃it )∥2 +
∥∇Lp (θ̃it )∥2 .
(46)
2
Here, the first inequality follows from the γ-smoothness of Lp , and the second inequality follows
from the update rule θ̃it+1 = θ̃it − ηp ∇Lp (θ̃it ).

529
530

531

536

537
538
539

540
541

542
543

2
β 3 ηg
≥ 0. Solving this inequality, we get ηg ≤ 2µ
2
β3 .

Proof of Lemma 3

Next, we use the strong convexity property of the loss function, which implies that:
∥∇Lp (θ̃it )∥2 ≥ µ2 ∥θ̃it − θ̃i∗ ∥2 .
(47)
Taking the expectation on both sides of the inequalities in Equation(46) and using the result from
Equation(47), we get:
ηp2 γ
E[Lp (θ̃it+1 )] ≤ E[Lp (θ̃it )] − ηp E[∥∇Lp (θ̃it )∥2 ] +
E[∥∇Lp (θ̃it )∥2 ]
(48)
2
ηp2 γσ 2
≤ E[Lp (θ̃it )] − µ2 ηp E[∥θ̃it − θ̃i∗ ∥2 ] +
.
(49)
2
To ensure the desired inequality holds, we need the coefficient of E[∥θ̃it − θ̃i∗ ∥2 ] to be non-negative.
µ2 γη
This leads to the condition µ2 − 2 p ≥ 0. Solving this inequality, we obtain ηp ≤ γ2 .

544

17

545

C.5

Convergence analysis for non-convex case

546

Proof. We start by defining the total loss function as the sum of the client, global, and personal losses:
F (θ) = Lc (θ) + La (θ) + Lp (θ).

547
548

(50)

Based on Lemma 4, Lemma 5, and Lemma 6, we can infer that the learning rates are equal, i.e.,
η = ηp = ηg .

551

Next, we combine the results from the individual lemmas to derive an inequality for the total loss
function:
ηL
3η 2 Lσ 2
E[F (θt+1 )] ≤ E[F (θt )] − η(1 −
)E[∥∇Lc (θit )∥2 + ∥∇La (θgt )∥2 + ∥∇Lp (θ̃it )∥2 ] +
.
2
2
(51)
This inequality provides a bound on the expected decrease in the total loss function at each iteration.

552

Summing this inequality over all iterations, we get:

549
550

T
X

T

E[F (θt ) − F (θt+1 )] ≥ η(1 −

t=1

ηL X
3T η 2 Lσ 2
)
E[∥∇F (θt )∥2 ] −
.
2 t=1
2

(52)

554

This inequality provides a lower bound on the sum of the expected differences in the total loss
function over all iterations.

555

Rearranging the terms, we obtain:

553

T
X

E[∥F (θt )∥2 ] ≤

t=1

F (θ1 ) − F (θ∗ )
3T ηLσ 2
+
η(1 − ηL
2(1 − ηL
2 )
2 )

2
3T ηLσ 2
(F (θ1 ) − F (θ∗ )) +
.
η(1 − ηL)
2 − ηL
Finally, dividing both sides by T , we get the desired result:
≤

556

(53)
(54)

558

2
3ηLσ 2
(F (θ1 ) − F (θ∗ )) +
.
(55)
ηT (1 − ηL)
2 − ηL
This inequality provides a bound on the expected squared norm of the total loss function at each
iteration.

559

C.5.1

560

Proof. We start by applying the update rule and the smoothness property of the loss function.
Specifically, for the local update on the client side, we have:
L
Lc (θit+1 ) ≤ Lc (θit ) + ⟨∇Lc (θit ), θit+1 − θit ⟩ + ∥θit+1 − θit ∥2 .
(56)
2
Given the update rule θit+1 = θit − η∇Lc (θit ; x, y), we substitute this into the above inequality:

E[∥F (θt )∥2 ] ≤

557

561

562

Proof of Lemma 4

η2 L
∥∇Lc (θit ; x, y)∥2 .
(57)
2
Next, we analyze the expected squared gradient norm. By the definition of the gradient and the noise
term, we have:
Lc (θit+1 ) ≤ Lc (θit ) + η⟨∇Lc (θit ), ∇Lc (θit ; x, y)⟩ +

563
564

E[∥∇Lc (θit ; x, y)∥2 ] = E[∥∇Lc (θit )∥2 ] + E[∥∇Lc (θit ; x, y)∥ − ∥∇Lc (θit )∥2 ]
≤ E[∥∇Lc (θit )∥2 ] + σ 2 .
565
566

(58)
(59)

Taking the expectation on both sides of the inequality in Equation(57) and using the result from
Equation(59), we get:
η2 L
(E[∥∇Lc (θit )∥2 ] + σ 2 )
2
ηL
η 2 Lσ 2
≤ E[∇Lc (θit )] − η(1 −
)E[∥∇Lc (θit )∥2 ] +
.
2
2

E[∇Lc (θit+1 )] ≤ E[Lc (θit )] − ηE[∥∇Lc (θit )∥2 ] +

567

18

(60)
(61)

568

C.5.2

569

Proof. The proof for Lemma 5 follows a similar structure to Lemma 4, focusing on the personal loss
function Lp . We start by applying the update rule and the smoothness property of the loss function.
Specifically, for the personal update on the client side, we have:

570
571

Proof of Lemma 5

Lp (θ̃it+1 ) ≤ Lp (θ̃it ) + ⟨∇Lp (θ̃it ), θ̃it+1 − θ̃it ⟩ +
572

574

ηp2 L
∥∇Lp (θ̃it ; x, y)∥2 .
2

≤ E[∥∇Lp (θ̃it )∥2 ] + σ 2 .
576

(63)

Next, we analyze the expected squared gradient norm. By the definition of the gradient and the noise
term, we have:
E[∥∇Lp (θ̃it ; x, y)∥2 ] = E[∥∇Lp (θ̃it )∥2 ] + E[∥∇Lp (θ̃it ; x, y)∥ − ∥∇Lp (θ̃it )∥2 ]

575

(62)

Given the update rule θ̃it+1 = θ̃it − ηp ∇Lp (θ̃it ; x, y), we substitute this into the above inequality:
Lp (θ̃it+1 ) ≤ Lp (θ̃it ) + ηp ⟨∇Lp (θ̃it ), ∇Lp (θ̃it ; x, y)⟩ +

573

L t+1
∥θ̃
− θ̃it ∥2 .
2 i

(64)
(65)

Taking the expectation on both sides of the inequality in Equation(63) and using the result from
Equation(65), we get:
ηp2 L
(E[∥∇Lp (θ̃it )∥2 ] + σ 2 )
2
ηp2 Lσ 2
ηp L
≤ E[∇Lp (θ̃it )] − ηp (1 −
)E[∥∇Lp (θ̃it )∥2 ] +
.
2
2

E[∇Lp (θ̃it+1 )] ≤ E[Lp (θ̃it )] − ηp E[∥∇Lp (θ̃it )∥2 ] +

(66)
(67)

577

578

C.5.3

579

Proof. The proof for Lemma 6 focuses on the aggregated loss function La . We start by applying the
update rule and the smoothness property of the loss function. Specifically, for the global update on
the server side, we have:

580
581

Proof of Lemma 6

La (θgt+1 ) ≤ La (θgt ) + ⟨∇La (θgt ), θgt+1 − θgt ⟩ +
582

584

ηg2 L
∥∇La (θgt ; x, y)∥2 .
2

≤ E[∥∇La (θgt )∥2 ] + σ 2 .
586

(69)

Next, we analyze the expected squared gradient norm. By the definition of the gradient and the noise
term, we have:
E[∥∇La (θgt ; x, y)∥2 ] = E[∥∇La (θgt )∥2 ] + E[∥∇La (θgt ; x, y)∥ − ∥∇La (θgt )∥2 ]

585

(68)

Given the update rule θgt+1 = θgt − ηg ∇La (θgt ; x, y), we substitute this into the above inequality:
La (θgt+1 ) ≤ La (θgt ) + ηg ⟨∇La (θgt ), ∇La (θgt ; x, y)⟩ +

583

L t+1
∥θ
− θgt ∥2 .
2 g

(70)
(71)

Taking the expectation on both sides of the inequality in Equation(69) and using the result from
Equation(71), we get:
ηg2 L
(E[∥∇La (θgt )∥2 ] + σ 2 )
2
ηg2 Lσ 2
ηg L
≤ E[∇La (θgt )] − ηg (1 −
)E[∥∇La (θgt )∥2 ] +
.
2
2

E[∇La (θgt+1 )] ≤ E[La (θgt )] − ηg E[∥∇La (θgt )∥2 ] +

587

19

(72)
(73)

588

589
590
591
592
593
594
595
596
597

D

Discussion

In this work, we address the challenge of client heterogeneity in heterogeneous federated learning.
To overcome these limitations, we propose FedFuse. FedFuse introduces a server-side Expertguided Fusion mechanism that uniquely facilitates adaptive knowledge fusion by dynamically gating
and weighting heterogeneous client knowledge contributions, moving beyond prior static schemes.
Complementarily, an elaborately designed selective knowledge distillation strategy allows clients to
assimilate global knowledge without blind imitation, thereby preserving crucial local model features
and mitigating detrimental model divergence. The effectiveness of our approach is supported by
theoretical analysis and extensive experiments conducted on various datasets and models for computer
vision tasks.

601

The limitations still remain. While FedFuse’s effectiveness has been demonstrated in near-real-world
settings, practical deployment on physical devices and in environments with extremely large models
remains untested due to resource constraints. Real-world implementation may uncover additional
challenges or limitations, providing further insights into the system’s scalability and efficiency.

602

E

Additional Experimental Details

603

E.1

Model Settings

604

Under the model-heterogeneous (HeteroFL) setting, we employ eight distinct model architectures for
collaborative training across clients, simulating diverse device capabilities. The specific architectures
assigned to clients depend on the total number of clients K. For experiments with K = 10, 50, 100,
models are assigned cyclically from the list below. In the model-homogeneous (HmFL) baseline
experiments, all clients utilize the ’Model 1’ architecture for fair comparison of personalization
algorithms without architectural confounding. The detailed model configurations used in the HeteroFL
setting are provided in Table 7.

598
599
600

605
606
607
608
609
610

Name
Model 1
Model 2
Model 3
Model 4
Model 5
Model 6
Model 7
Model 8
Homo.

Table 7: Model Configurations used in Heterogeneous Experiments.
Base Architecture Key Features
Layers
Simple CNN
2 Conv layers, 3 FC layers
5
ResNet18
BasicBlock, [2,2,2,2] layers
18
ResNet34
BasicBlock, [3,4,6,3] layers
34
ResNet50
Bottleneck, [3,4,6,3] layers
50
ResNet101
Bottleneck, [3,4,23,3] layers
101
ResNet152
Bottleneck, [3,8,36,3] layers
152
GoogleNet
Inception modules
22
MobileNetV2
Linear Bottlenecks, Depthwise Separable Conv
53
Simple CNN
2 Conv layers, 3 FC layers
5

Params.
≈3.2M
≈11.2M
≈21.3M
≈23.5M
≈42.5M
≈58.2M
≈6.8M
≈3.5M
≈3.2M

611

E.2

612

All experiments were conducted using PyTorch version 2.1.0 on NVIDIA 4090D GPUs with CUDA
12.1. Key hyperparameters included:

613

Implementation Details

614

• Optimizer: AdamW for both client and server updates.

615

• Client Local Training Learning Rate (η): 0.001

616

• Server Aggregation Learning Rate (ηg ): 0.001

617

• Client Personalization Update Learning Rate (ηp ): 0.001

618

• Local Epochs (Ec ): 1

619

• Server Epochs (Es ): 1

620

• Batch Size (Local Training): 64

621

• Batch Size (Server Aggregation / Local Update on Db ): 64

622

• Temperature (τ ): 2.0
20

Table 8: Comparison with the SOTA methods on three datasets in 50 clients.
Acc(%)
HmFL

HeteroFL

Algorithm
FedAvg
Per-FedAvg
FedProx
FedPer
FedFuse
FedKD
FedProto
FedMRL
FedTGP
FedFuse

CIFAR-100
17.57
28.08
18.11
30.51
35.33 (↑15.7%)
30.37
25.61
30.91
25.93
36.53 (↑18.2%)

Tiny-Imagenet
14.38
25.49
14.18
26.42
32.11 (↑21.5%)
18.58
15.46
16.12
25.13
32.68 (↑26.8%)

Flowers102
22.81
41.52
21.66
47.86
48.95 (↑2.3%)
26.71
12.74
26.32
27.87
39.48 (↑41.6%)

Table 9: Comparison with the SOTA methods on three datasets in 100 clients.
Acc(%)

Algorithm

CIFAR-100

Tiny-Imagenet

Flowers102

HmFL

FedAvg
Per-FedAvg
FedProx
FedPer
FedFuse

11.30
20.38
11.23
28.40
29.80 (↑4.9%)

10.44
20.18
13.10
23.15
33.44 (↑44.4%)

14.15
35.14
14.53
38.05
41.95 (↑10.2%)

HeteroFL

FedKD
FedProto
FedMRL
FedTGP
FedFuse

16.75
18.75
23.97
16.70
31.90 (↑33.1%)

13.28
12.04
11.39
26.88
30.98 (↑22.5%)

18.42
19.90
26.87
21.78
30.56 (↑13.7%)

• MoE Experts (E): 100 for K=100, 50 for K=50, 10 for K=10
• MoE top-k (k): 40 for K=100, 20 for K=50, 4 for K=10
• Communication Rounds (T ): 100
• Client Participation Rate (p): 0.1 for K=100, 0.2 for K=50, 1.0 for K=10
• Public Dataset (Db ): A subset of the corresponding dataset
• Data Heterogeneity (α for Dirichlet): As specified in Table 2 (0.05, 0.1, 0.5). For IID
experiments, data was shuffled and distributed uniformly.

623
624
625
626
627
628
629

631

Baseline implementations were based on publicly available codebases where possible, adapted to the
HeteroFL setting.

632

E.3

633

Tables 8 and 9 present the detailed accuracy comparisons on the three datasets for scenarios with 50
and 100 clients, respectively, complementing Table 1 in the main text. Figure 4 illustrates the training
convergence curves under the model-homogeneous setting, serving as a baseline comparison for the
heterogeneous results shown in Figure 3.

630

634
635
636

Additional Results

21

   
   

 $ F F X U D F \

   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   
   

 

  

  

  

 5 R X Q G

  

   
   
   

   

(a) CIFAR-100, K = 10

  

  

 5 R X Q G

  

   

 

   

  

  

  

 5 R X Q G

  

   
   

   

(d) Tiny-Imagenet, K = 10
   

 $ F F X U D F \

   
   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   
   
   
 

  

  

  

 5 R X Q G

  

(g) Flowers102, K = 10

   

  

  

  

 5 R X Q G

  

   

 

  

  

  

 5 R X Q G

  

   

(f) Tiny-Imagenet, K = 100
   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   
   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   

   
   

   

   

   

(e) Tiny-Imagenet, K = 50
   

  

   

 

   

  

 5 R X Q G

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   

   

 

  

(c) CIFAR-100, K = 100

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   

   

  

   

 $ F F X U D F \

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

   

(b) CIFAR-100, K = 50

 $ F F X U D F \

 $ F F X U D F \

  

   

   

   

   

 

   

 $ F F X U D F \

   

 $ F F X U D F \

 $ F F X U D F \

   

   

 ) H G 3 U R W R
 ) H G . '
 ) H G 7 * 3
 ) H G 0 5 /
 2 X U V

 $ F F X U D F \

   

   
   
   
   

 

  

  

  

 5 R X Q G

  

(h) Flowers102, K = 50

   

 

  

  

  

 5 R X Q G

  

   

(i) Flowers102, K = 100

Figure 3: Training accuracy curves for various algorithms in heterogeneous federated learning (HeteroFL) across
datasets and client numbers K.

22

   

   

   

   

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   
   

 

  

  

  

  

 5 R X Q G

   

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   
   

   

 

(a) CIFAR-100, K = 10

  

  

  

 5 R X Q G

 

 $ F F X U D F \

   

  

  

  

 5 R X Q G

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   

(d) Tiny-Imagenet, K = 10

  

  

  

  

 5 R X Q G

   

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

  

  

  

  

 5 R X Q G

   
   

   

  

 5 R X Q G

  

   

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   

   

 

  

(f) Tiny-Imagenet, K = 100

 $ F F X U D F \

   

  

   

   

 $ F F X U D F \

   

   

   

 

(e) Tiny-Imagenet, K = 50

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   

   

   

   

  

   

 

   

  

 5 R X Q G

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   

   

   

  

(c) CIFAR-100, K = 100

   

  

  

   

   

   

 

   

   

 $ F F X U D F \

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

   

   

   

   

   

 $ F F X U D F \

  

 ) H G $ Y J
 3 H U  ) H G $ Y J
 ) H G 3 U R [
 ) H G 3 H U
 2 X U V

(b) CIFAR-100, K = 50

   

 $ F F X U D F \

 $ F F X U D F \

   

   

 $ F F X U D F \

 $ F F X U D F \

   

   
   
   

 

(g) Flowers102, K = 10

  

  

  

  

 5 R X Q G

   

 

(h) Flowers102, K = 50

  

  

  

 5 R X Q G

  

   

(i) Flowers102, K = 100

Figure 4: Training accuracy curves for various algorithms under the homogeneous federated learning setting
(HmFL) across different datasets and client numbers (K). These serve as a baseline for evaluating personalization
methods when architectural heterogeneity is absent.

    

    

 ' L I I H U H Q F H  9 D O X H

   

   

    

    

   
    

    
    

   

    

    

   
 

 

 

 

 

 

 1 X P E H U  R I  ( [ S H U W

 

 

  

Figure 5: Difference Accuracy on different number of selected experts
42.22
37.98
35.90
33.20 32.71

36.53
31.90

30.37 30.91
25.61 25.93

23.97
18.75
16.75
16.70

10 clients

50 clients

100 clients

Number of Clients

FedKD
FedProto
FedMRL
FedTGP
Ours
23.27
15.62
12.90
12.50
10.71

52.46

50

Accuracy(%)

Accuracy(%)

40
35
30
25
20
15
10
5
0

40

41.39
39.24
37.68

30

27.05

39.48

27.87
26.71 26.32

20

0

(a) CIFAR-100

26.87
19.90
18.42

21.78

12.74

10

500 clients

30.56

10 clients

50 clients

FedKD
FedProto
FedMRL
FedTGP
Ours

100 clients

Number of Clients

15.15
13.54 14.66
12.74
10.28

500 clients

(b) Flowers102

Figure 6: Scalability: Accuracy vs. Number of Clients (K) on CIFAR-100 and Flowers102 (HeteroFL Setting).

23

637
638
639
640
641
642
643
644
645
646
647
648
649
650
651
652
653
654
655
656
657
658
659
660
661
662
663
664
665
666
667
668
669
670
671
672
673
674
675
676
677
678
679
680
681
682
683
684

NeurIPS Paper Checklist
1. Claims
Question: Do the main claims made in the abstract and introduction accurately reflect the
paper’s contributions and scope?
Answer: [Yes] .
Justification: We have faithfully stated our contributions in both the abstract and introduction.
2. Limitations
Question: Does the paper discuss the limitations of the work performed by the authors?
Answer: [Yes]
Justification: We discuss the limitations of the works in the conclusion.
3. Theory assumptions and proofs
Question: For each theoretical result, does the paper provide the full set of assumptions and
a complete (and correct) proof?
Answer: [Yes] .
Justification: The regulatory theoretical results are stated in Section 3.6. Due to space
limitations, we are unable to present all the missing assumptions, proofs and intermediate
results in the main text. They are deferred to the Appendix. Please refer to Appendix C.2
for details.
4. Experimental result reproducibility
Question: Does the paper fully disclose all the information needed to reproduce the main experimental results of the paper to the extent that it affects the main claims and/or conclusions
of the paper (regardless of whether the code and data are provided or not)?
Answer: [Yes] .
Justification: We provide detailed experimental and hyperparameter setups in Section 4 and
Appendix E.
5. Open access to data and code
Question: Does the paper provide open access to the data and code, with sufficient instructions to faithfully reproduce the main experimental results, as described in supplemental
material?
Answer: [Yes] .
Justification: Our evaluations are based on open-accessed datasets that are publicly available.
An official implementation code is provided in supplemental material.
6. Experimental setting/details
Question: Does the paper specify all the training and test details (e.g., data splits, hyperparameters, how they were chosen, type of optimizer, etc.) necessary to understand the
results?
Answer: [Yes] .
Justification: Experimental setting/details are important parts of reproducing our results. We
provide the details in Section 4 and Appendix E to the best of our ability.
7. Experiment statistical significance
Question: Does the paper report error bars suitably and correctly defined or other appropriate
information about the statistical significance of the experiments?
Answer: [Yes] .
Justification: Our results are averaged over multiple random seeds.
8. Experiments compute resources
Question: For each experiment, does the paper provide sufficient information on the computer resources (type of compute workers, memory, time of execution) needed to reproduce
the experiments?
24

685

Answer: [Yes] .

686

Justification: Please find the hardware specifications in Section 4.

687

9. Code of ethics

689

Question: Does the research conducted in the paper conform, in every respect, with the
NeurIPS Code of Ethics https://neurips.cc/public/EthicsGuidelines?

690

Answer: [Yes] .

691

Justification: The NeurIPS code of ethics is strictly enforced throughout our research.

688

692

10. Broader impacts

694

Question: Does the paper discuss both potential positive societal impacts and negative
societal impacts of the work performed?

695

Answer: [Yes] .

696

Justification: We have discussed broader impacts in Section 1. We are unaware of any
negative impacts.

693

697
698

11. Safeguards

701

Question: Does the paper describe safeguards that have been put in place for responsible
release of data or models that have a high risk for misuse (e.g., pretrained language models,
image generators, or scraped datasets)?

702

Answer: [NA] .

703

Justification: The paper poses no such risks.

699
700

704

12. Licenses for existing assets

707

Question: Are the creators or original owners of assets (e.g., code, data, models), used in
the paper, properly credited and are the license and terms of use explicitly mentioned and
properly respected?

708

Answer: [Yes] .

709

Justification: The existing assets used in this paper has been adequately cited or credited to.

705
706

710

13. New assets

712

Question: Are new assets introduced in the paper well documented and is the documentation
provided alongside the assets?

713

Answer: [Yes] .

714

Justification: We have documented the experiment details in Section 4 and Appendix E. In
addition, we provide our code with clear details and examples.

711

715
716

14. Crowdsourcing and research with human subjects

719

Question: For crowdsourcing experiments and research with human subjects, does the paper
include the full text of instructions given to participants and screenshots, if applicable, as
well as details about compensation (if any)?

720

Answer: [NA] .

721

Justification: The paper does not involve crowdsourcing nor research with human subjects.

722

15. Institutional review board (IRB) approvals or equivalent for research with human
subjects

717
718

723

727

Question: Does the paper describe potential risks incurred by study participants, whether
such risks were disclosed to the subjects, and whether Institutional Review Board (IRB)
approvals (or an equivalent approval/review based on the requirements of your country or
institution) were obtained?

728

Answer: [NA] .

729

Justification: The paper does not involve crowdsourcing nor research with human subjects.

724
725
726

730

16. Declaration of LLM usage
25

731
732
733
734
735
736

Question: Does the paper describe the usage of LLMs if it is an important, original, or
non-standard component of the core methods in this research? Note that if the LLM is used
only for writing, editing, or formatting purposes and does not impact the core methodology,
scientific rigorousness, or originality of the research, declaration is not required.
Answer: [NA]
Justification: The paper does not involve the usage of LLMs.

26

